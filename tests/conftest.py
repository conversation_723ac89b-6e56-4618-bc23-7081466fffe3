"""
Pytest configuration and fixtures for AutoComposeAgent tests
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from PIL import Image
import numpy as np
import yaml
import sys
import os

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autocompose.core.profile import Profile


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_image():
    """Create a sample test image"""
    # Create a simple RGB image
    width, height = 800, 600
    image_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add some patterns
    # Sky (blue gradient)
    for y in range(height // 2):
        intensity = int(100 + (y / (height // 2)) * 100)
        image_array[y, :] = [intensity // 2, intensity // 2, intensity]
    
    # Ground (green)
    image_array[height // 2:, :] = [50, 120, 50]
    
    # Add a "sun" (yellow circle)
    sun_x, sun_y = width - 150, 100
    sun_radius = 30
    for y in range(max(0, sun_y - sun_radius), min(height, sun_y + sun_radius)):
        for x in range(max(0, sun_x - sun_radius), min(width, sun_x + sun_radius)):
            if (x - sun_x) ** 2 + (y - sun_y) ** 2 <= sun_radius ** 2:
                image_array[y, x] = [255, 255, 100]
    
    return Image.fromarray(image_array)


@pytest.fixture
def sample_config():
    """Create a sample configuration dictionary"""
    return {
        'profile': {
            'perception_model': 'llama-vision',
            'aesthetic_models': ['NIMA'],
            'restore_preference': 'composition',
            'rollback_threshold': 0.5,
            'tasks': ['crop', 'rotate'],
            'max_iterations': 3,
            'device': 'cpu',
            'batch_size': 1
        },
        'api': {
            'openai_api_key': None,
            'anthropic_api_key': None
        },
        'logging': {
            'level': 'INFO',
            'save_intermediate': False,
            'output_format': 'jpg'
        },
        'performance': {
            'use_gpu': False,
            'num_workers': 1,
            'memory_efficient': True
        }
    }


@pytest.fixture
def config_file(temp_dir, sample_config):
    """Create a temporary config file"""
    config_path = temp_dir / "test_config.yaml"
    with open(config_path, 'w') as f:
        yaml.dump(sample_config, f)
    return config_path


@pytest.fixture
def profile(config_file):
    """Create a Profile instance with test configuration"""
    return Profile(config_file)


@pytest.fixture
def mock_torch():
    """Mock torch for tests that don't need actual PyTorch"""
    class MockTorch:
        @staticmethod
        def cuda_is_available():
            return False
        
        class tensor:
            def __init__(self, data):
                self.data = data
            
            def item(self):
                return 0.5
    
    return MockTorch()


@pytest.fixture
def sample_perception_result():
    """Create a sample perception result for testing"""
    from autocompose.agents.unified_types import PerceptionResult
    
    return PerceptionResult(
        content_description="A landscape image with sky and ground",
        composition_issues=["center_bias", "low_contrast"],
        recommendations=["Apply rule of thirds", "Increase contrast"]
    )


@pytest.fixture
def sample_editing_instruction():
    """Create a sample editing instruction for testing"""
    from autocompose.agents.planning import EditingInstruction

    return EditingInstruction(
        instruction="Crop the image to improve composition using rule of thirds",
        priority=1
    )


@pytest.fixture
def sample_adjustment_plan(sample_editing_instruction):
    """Create a sample adjustment plan for testing"""
    from autocompose.agents.planning import AdjustmentPlan

    return AdjustmentPlan(
        instructions=[sample_editing_instruction],
        reasoning="Test plan for improving composition"
    )


# Test markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "requires_gpu: marks tests that require GPU"
    )
    config.addinivalue_line(
        "markers", "requires_api: marks tests that require API keys"
    )


# Skip tests based on availability
def pytest_collection_modifyitems(config, items):
    """Modify test collection based on available resources"""
    skip_gpu = pytest.mark.skip(reason="GPU not available")
    skip_api = pytest.mark.skip(reason="API keys not configured")
    
    for item in items:
        # Skip GPU tests if CUDA not available
        if "requires_gpu" in item.keywords:
            try:
                import torch
                if not torch.cuda.is_available():
                    item.add_marker(skip_gpu)
            except ImportError:
                item.add_marker(skip_gpu)
        
        # Skip API tests if keys not configured
        if "requires_api" in item.keywords:
            if not (os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY")):
                item.add_marker(skip_api)
