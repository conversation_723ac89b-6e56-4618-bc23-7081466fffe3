"""
Integration tests for AutoComposeAgent
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from PIL import Image
import numpy as np

from autocompose.main import AutoComposeAgent
from autocompose.core.profile import Profile


@pytest.mark.integration
class TestAutoComposeAgentIntegration:
    """Integration tests for the complete AutoComposeAgent workflow"""
    
    def test_agent_initialization(self, config_file):
        """Test agent initialization with config"""
        agent = AutoComposeAgent(config_file)
        assert agent.profile is not None
        assert agent.perception_agent is not None
        assert agent.planning_agent is not None
        assert agent.restoration_agent is not None
    
    def test_agent_initialization_no_config(self):
        """Test agent initialization without config"""
        agent = AutoComposeAgent()
        assert agent.profile is not None
        assert agent.perception_agent is not None
        assert agent.planning_agent is not None
        assert agent.restoration_agent is not None
    
    @pytest.mark.slow
    def test_process_image_basic(self, sample_image, temp_dir):
        """Test basic image processing workflow"""
        # Create agent with CPU-only config
        config_data = {
            'profile': {
                'perception_model': 'llama-vision',
                'aesthetic_models': ['NIMA'],
                'device': 'cpu',
                'tasks': ['crop', 'rotate'],
                'max_iterations': 1
            }
        }
        
        config_path = temp_dir / "test_config.yaml"
        import yaml
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f)
        
        agent = AutoComposeAgent(config_path)
        
        # Process image
        result = agent.process_image(sample_image)
        
        # Verify result structure
        assert hasattr(result, 'final_image')
        assert hasattr(result, 'final_score')
        assert hasattr(result, 'improvement')
        assert hasattr(result, 'applied_operations')
        assert hasattr(result, 'restoration_method')

        # Verify result values
        assert isinstance(result.final_image, Image.Image)
        assert isinstance(result.final_score, (int, float))
        assert isinstance(result.improvement, (int, float))
        assert isinstance(result.applied_operations, list)
        assert isinstance(result.restoration_method, str)
        
        # Score should be reasonable
        assert 0 <= result.final_score <= 1
    
    def test_process_image_with_output_path(self, sample_image, temp_dir):
        """Test image processing with output path"""
        agent = AutoComposeAgent()
        output_path = temp_dir / "output.jpg"
        
        result = agent.process_image(sample_image, output_path=str(output_path))
        
        # Output file should be created
        assert output_path.exists()
        
        # Verify saved image
        saved_image = Image.open(output_path)
        assert saved_image.size == result.final_image.size
    
    def test_process_multiple_images(self, temp_dir):
        """Test processing multiple images"""
        # Create multiple test images
        images = []
        for i in range(3):
            # Create different test images
            width, height = 400 + i * 100, 300 + i * 50
            image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            images.append(Image.fromarray(image_array))
        
        agent = AutoComposeAgent()
        results = []
        
        for i, image in enumerate(images):
            output_path = temp_dir / f"output_{i}.jpg"
            result = agent.process_image(image, output_path=str(output_path))
            results.append(result)
            
            # Verify each result
            assert isinstance(result.final_image, Image.Image)
            assert output_path.exists()
        
        # All results should be valid
        assert len(results) == 3
    
    def test_agent_with_different_models(self, sample_image):
        """Test agent with different model configurations"""
        # Test with different aesthetic models
        configs = [
            {'aesthetic_models': ['NIMA'], 'device': 'cpu'},
            {'aesthetic_models': ['CLIPIQA'], 'device': 'cpu'},
            {'aesthetic_models': ['NIMA', 'CLIPIQA'], 'device': 'cpu'}
        ]
        
        for config in configs:
            profile_config = {
                'profile': {
                    'perception_model': 'llama-vision',
                    'max_iterations': 1,
                    **config
                }
            }
            
            # Create temporary config
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                import yaml
                yaml.dump(profile_config, f)
                config_path = f.name
            
            try:
                agent = AutoComposeAgent(config_path)
                result = agent.process_image(sample_image)
                
                # Should complete without errors
                assert result is not None
                assert isinstance(result.final_image, Image.Image)
                
            finally:
                Path(config_path).unlink()
    
    def test_agent_error_handling(self):
        """Test agent error handling with invalid inputs"""
        agent = AutoComposeAgent()
        
        # Test with None image
        with pytest.raises((ValueError, AttributeError)):
            agent.process_image(None)
        
        # Test with invalid image format
        invalid_image = "not_an_image"
        with pytest.raises((ValueError, AttributeError)):
            agent.process_image(invalid_image)
    
    def test_agent_with_minimal_image(self):
        """Test agent with minimal image size"""
        # Create very small image
        tiny_image = Image.new("RGB", (10, 10), (255, 255, 255))
        
        agent = AutoComposeAgent()
        result = agent.process_image(tiny_image)
        
        # Should handle gracefully
        assert result is not None
        assert isinstance(result.final_image, Image.Image)
    
    def test_agent_with_large_image(self):
        """Test agent with large image"""
        # Create larger image (but not too large for testing)
        large_array = np.random.randint(0, 255, (1200, 1600, 3), dtype=np.uint8)
        large_image = Image.fromarray(large_array)
        
        agent = AutoComposeAgent()
        result = agent.process_image(large_image)
        
        # Should handle gracefully
        assert result is not None
        assert isinstance(result.final_image, Image.Image)
    
    def test_agent_performance_logging(self, sample_image, temp_dir):
        """Test performance logging functionality"""
        # Create config with performance logging enabled
        config_data = {
            'profile': {
                'perception_model': 'llama-vision',
                'aesthetic_models': ['NIMA'],
                'device': 'cpu',
                'max_iterations': 1
            },
            'logging': {
                'level': 'DEBUG',
                'save_intermediate': True
            }
        }
        
        config_path = temp_dir / "perf_config.yaml"
        import yaml
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f)
        
        agent = AutoComposeAgent(config_path)
        result = agent.process_image(sample_image)
        
        # Should complete with performance logging
        assert result is not None
        assert hasattr(agent, 'performance_logger')
    
    def test_agent_task_filtering(self, sample_image):
        """Test agent with specific task filtering"""
        # Test with only crop task
        config_data = {
            'profile': {
                'perception_model': 'llama-vision',
                'aesthetic_models': ['NIMA'],
                'device': 'cpu',
                'tasks': ['crop'],  # Only crop
                'max_iterations': 1
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            agent = AutoComposeAgent(config_path)
            result = agent.process_image(sample_image)
            
            # Should only apply crop tasks
            assert result is not None
            # Check if only crop tasks were applied (if any tasks were applied)
            if result.applied_tasks:
                crop_tasks = [task for task in result.applied_tasks if 'crop' in str(task).lower()]
                assert len(crop_tasks) >= 0  # Should have crop tasks or no tasks
                
        finally:
            Path(config_path).unlink()


@pytest.mark.integration
@pytest.mark.slow
class TestEndToEndWorkflow:
    """End-to-end workflow tests"""
    
    def test_complete_workflow_with_comparison(self, temp_dir):
        """Test complete workflow with before/after comparison"""
        # Create a test image with obvious composition issues
        width, height = 800, 600
        image_array = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Create an off-center subject
        subject_x, subject_y = width // 4, height // 4  # Off-center
        subject_size = 50
        
        for y in range(max(0, subject_y - subject_size), min(height, subject_y + subject_size)):
            for x in range(max(0, subject_x - subject_size), min(width, subject_x + subject_size)):
                image_array[y, x] = [255, 100, 100]  # Red subject
        
        original_image = Image.fromarray(image_array)
        
        # Process with agent
        agent = AutoComposeAgent()
        result = agent.process_image(original_image)
        
        # Save comparison
        from autocompose.utils.image_ops import ImageOperations
        comparison = ImageOperations.create_comparison_grid(
            [original_image, result.final_image],
            labels=["Original", "Enhanced"]
        )
        
        comparison_path = temp_dir / "comparison.jpg"
        comparison.save(comparison_path)
        
        assert comparison_path.exists()
        assert result.final_image.size[0] > 0
        assert result.final_image.size[1] > 0
    
    def test_iterative_improvement(self, sample_image):
        """Test iterative improvement process"""
        config_data = {
            'profile': {
                'perception_model': 'llama-vision',
                'aesthetic_models': ['NIMA'],
                'device': 'cpu',
                'max_iterations': 3,  # Multiple iterations
                'rollback_threshold': 0.1
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            agent = AutoComposeAgent(config_path)
            result = agent.process_image(sample_image)
            
            # Should complete iterative process
            assert result is not None
            assert result.total_steps_executed >= 1
            
        finally:
            Path(config_path).unlink()
