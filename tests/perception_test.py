"""
测试脚本：测试 PerceptionAgent 的各种输入输出情况

# 核心逻辑和执行流程
--------------------------------
该测试脚本用于全面测试 PerceptionAgent 类的图像分析功能，包括基本功能测试、不同模型测试和边缘情况测试。

## 主要函数及其作用
1. 辅助函数:
   - create_test_image(): 创建各种类型的测试图像（渐变、纯色、随机噪声）
   - create_sample_profile(): 创建用于测试的临时配置文件
   - visualize_result(): 可视化分析结果（图像+文本描述）
   - print_result(): 格式化打印分析结果

2. 测试函数:
   - test_basic_functionality(): 测试基本功能，包括:
     * 使用PIL Image对象作为输入
     * 使用图像路径作为输入
     * (注释掉的)获取构图评分和比较两个图像
   
   - test_different_models(): 测试不同的视觉模型，包括:
     * llama-vision
     * gpt-4-vision
     * claude-vision
     分析同一图像，比较结果和性能差异
   
   - test_edge_cases(): 测试边缘情况，包括:
     * 极小尺寸图像(50x50)
     * 极大尺寸图像(3000x2000)
     * 灰度图像
     * 模型不可用的回退情况

## 执行流程
1. 脚本从main()函数开始执行
2. main()函数依次调用三个测试函数
3. 每个测试函数的执行流程为:
   - 创建必要的配置文件
   - 初始化PerceptionAgent
   - 创建适合测试场景的图像
   - 执行相应的测试操作
   - 打印/可视化结果
   - 清理临时文件

## 使用方法
直接运行此脚本: python perception_test.py
确保项目依赖已安装，且相关API密钥已在环境变量中设置(如需测试特定模型)
"""

import os
import sys
import json
import time
import yaml
import tempfile
from pathlib import Path
from typing import Dict, Any, Union, List
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import matplotlib.pyplot as plt

# 确保能找到项目模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autocompose.agents.perception import PerceptionAgent
from autocompose.agents.unified_types import PerceptionResult
from autocompose.core.profile import Profile




def create_sample_profile(model_name="llama-vision", api_keys=None):
    """创建测试用的配置文件"""
    # 创建基本配置
    profile_config = {
        'profile': {
            'perception_model': model_name,
            # 'aesthetic_models': ['NIMA'],
            'device': 'cuda',
            'max_iterations': 1
        }
    }
    
    # 添加API密钥
    if api_keys:
        profile_config['api'] = api_keys
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(profile_config, f)
        config_path = f.name
    
    return config_path


def visualize_result(image: Image.Image, result: PerceptionResult, save_path=None):
    """可视化感知分析结果"""
    plt.figure(figsize=(12, 10))
    
    # 显示原始图像
    plt.subplot(2, 1, 1)
    plt.imshow(np.array(image))
    plt.title("Original Image")
    plt.axis('off')
    
    # 显示分析结果
    plt.subplot(2, 1, 2)
    plt.axis('off')
    result_text = (
        f"Content: {result.content_description[:100]}...\n\n"
        f"Issues Found: {len(result.composition_issues)}\n\n"
        f"Composition Issues:\n" +
        "\n".join([f"- {issue}" for issue in result.composition_issues[:3]]) +
        "\n\nRecommendations:\n" +
        "\n".join([f"- {rec}" for rec in result.recommendations[:3]])
    )
    plt.text(0.1, 0.9, result_text, fontsize=9, 
             verticalalignment='top', wrap=True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"结果保存至: {save_path}")
    else:
        plt.show()


def print_result(result: Union[PerceptionResult, Dict, float], title="分析结果"):
    """打印分析结果"""
    print("\n" + "="*50)
    print(f" {title} ".center(50, "="))
    print("="*50)
    
    if isinstance(result, PerceptionResult):
        print(f"内容描述: {result.content_description}")
        print(f"问题数量: {len(result.composition_issues)}")
        print("\n构图问题:")
        for issue in result.composition_issues:
            print(f"- {issue}")
        print("\n建议:")
        for rec in result.recommendations:
            print(f"- {rec}")
    
    elif isinstance(result, dict):
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    else:  # float
        print(f"评分: {result:.2f}")
    
    print("="*50 + "\n")


def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试 PerceptionAgent 基本功能")
    
    # 创建配置文件
    config_path = create_sample_profile()
    try:
        # 初始化PerceptionAgent
        profile = Profile(config_path)
        agent = PerceptionAgent(profile)
        
        # 创建测试图像
        gradient_image = 'input.png'
        
        # 测试1: 使用PIL Image对象作为输入
        print("\n测试1: 使用PIL Image对象作为输入")
        result1 = agent.analyze_image(gradient_image)
        print_result(result1, "测试1结果 - 渐变图像分析")
        
        # 测试2: 使用图像路径作为输入
        print("\n测试2: 使用图像路径作为输入")
        result2 = agent.analyze_image(gradient_image)
        print_result(result2, "测试2结果 - 通过路径分析")
        
        # # 测试3: 获取构图评分
        # print("\n测试3: 获取构图评分")
        # score = agent.get_composition_score(solid_image)
        # print_result(score, "测试3结果 - 纯色图像评分")
        
        # # 测试4: 比较两个图像
        # print("\n测试4: 比较两个图像")
        # comparison = agent.compare_images(gradient_image, solid_image)
        # print_result(comparison, "测试4结果 - 图像比较")
        
        # # 可视化结果
        # result_path = os.path.join(temp_dir, "result.png")
        # visualize_result(gradient_image, result1, result_path)
        
    finally:
        # 清理临时文件
        if os.path.exists(config_path):
            os.unlink(config_path)


def test_different_models():
    """测试不同的模型配置"""
    print("\n🔍 测试不同的感知模型")
    
    # 可用的模型名称
    model_names = ["llama-vision"]#, "gpt-4-vision", "claude-vision"]
    
    # 创建API密钥字典 (实际使用时应该从环境变量或安全存储中获取)
    # 注意: 这里只是示例，实际使用应该提供有效的API密钥
    api_keys = {
        "openai_api_key": os.environ.get("OPENAI_API_KEY", "your_openai_key"),
        "anthropic_api_key": os.environ.get("ANTHROPIC_API_KEY", "your_anthropic_key")
    }
    
    # 创建测试图像
    test_image = 'input.png'
    
    for model_name in model_names:
        print(f"\n测试模型: {model_name}")
        
        # 创建配置文件
        config_path = create_sample_profile(model_name, api_keys)
        
        try:
            # 初始化PerceptionAgent
            profile = Profile(config_path)
            agent = PerceptionAgent(profile)
            
            # 分析图像
            start_time = time.time()
            result = agent.analyze_image(test_image)
            elapsed_time = time.time() - start_time
            
            # 打印结果
            print_result(result, f"{model_name} 分析结果")
            print(f"分析耗时: {elapsed_time:.2f}秒")
            
        except Exception as e:
            print(f"模型 {model_name} 测试失败: {str(e)}")
        
        finally:
            # 清理临时文件
            if os.path.exists(config_path):
                os.unlink(config_path)


def test_edge_cases():
    """测试边缘情况"""
    print("\n🔍 测试边缘情况")
    
    # 创建配置文件
    config_path = create_sample_profile()
    
    try:
        # 初始化PerceptionAgent
        profile = Profile(config_path)
        agent = PerceptionAgent(profile)
        
        # 测试1: 极小尺寸图像
        print("\n测试1: 极小尺寸图像")
        tiny_image = create_test_image(size=(50, 50), pattern="solid")
        try:
            result = agent.analyze_image(tiny_image)
            print_result(result, "极小图像分析结果")
        except Exception as e:
            print(f"极小图像分析失败: {str(e)}")
        
        # 测试2: 极大尺寸图像
        print("\n测试2: 极大尺寸图像")
        large_image = create_test_image(size=(3000, 2000), pattern="gradient")
        try:
            result = agent.analyze_image(large_image)
            print_result(result, "极大图像分析结果")
        except Exception as e:
            print(f"极大图像分析失败: {str(e)}")
        
        # 测试3: 灰度图像
        print("\n测试3: 灰度图像")
        gray_image = create_test_image(color_mode="L", pattern="noise")
        try:
            result = agent.analyze_image(gray_image)
            print_result(result, "灰度图像分析结果")
        except Exception as e:
            print(f"灰度图像分析失败: {str(e)}")
        
        # 测试4: 模型不可用的情况
        print("\n测试4: 模型不可用的情况")
        # 创建一个不存在的模型配置
        invalid_config_path = create_sample_profile("nonexistent_model")
        invalid_profile = Profile(invalid_config_path)
        fallback_agent = PerceptionAgent(invalid_profile)
        
        try:
            result = fallback_agent.analyze_image(create_test_image())
            print_result(result, "回退分析结果")
        except Exception as e:
            print(f"回退分析失败: {str(e)}")
        
    finally:
        # 清理临时文件
        if os.path.exists(config_path):
            os.unlink(config_path)


def main():
    """主函数"""
    print("="*60)
    print(" PerceptionAgent 测试脚本 ".center(60, "="))
    print("="*60)
    
    # 运行测试
    test_basic_functionality()
    # test_different_models()
    # test_edge_cases()
    
    print("\n所有测试完成!")


if __name__ == "__main__":
    main()
