# 指定 PyTorch 与 CUDA 12.1 支持
--extra-index-url https://download.pytorch.org/whl/cu121

torch==2.3.0+cu121
torchvision==0.18.0+cu121
torchaudio==2.3.0+cu121

# Transformers & Diffusion
transformers>=4.30.0
diffusers>=0.18.0
accelerate>=0.20.0

# Image processing
Pillow>=9.0.0
opencv-python>=4.7.0
scikit-image>=0.20.0
albumentations>=1.3.0

# ML and evaluation
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.2.0
matplotlib>=3.6.0
seaborn>=0.12.0

# LLM/VLM support
openai>=1.0.0
anthropic>=0.3.0
dashscope>=1.14.0
requests>=2.28.0

# Configuration and utilities
pydantic>=2.0.0
PyYAML>=6.0
tqdm>=4.64.0
click>=8.1.0
rich>=13.0.0

# Aesthetic evaluation models
timm>=0.9.0
clip-by-openai>=1.0
torchmetrics>=0.11.0

# GPU 加速支持和量化
xformers>=0.0.20
bitsandbytes>=0.41.0
# flash-attn  # Optional, requires specific CUDA setup
