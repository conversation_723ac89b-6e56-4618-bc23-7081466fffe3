2025-08-02 20:07:25 - autocompose.main - INFO - setup_logging:173 - Logging initialized - Level: INFO, File: logs/autocompose_20250802_200725.log
2025-08-02 20:07:25 - autocompose.system - INFO - log_system_info:191 - Python version: 3.9.7 | packaged by conda-forge | (default, Sep  2 2021, 17:58:34) 
[GCC 9.4.0]
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:192 - Platform: Linux-3.10.0-957.el7.x86_64-x86_64-with-glibc2.17
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:193 - Architecture: ('64bit', 'ELF')
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:198 - PyTorch version: 2.5.1+cu121
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:199 - CUDA available: True
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:201 - CUDA version: 12.1
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:202 - GPU count: 1
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:205 - GPU 0: NVIDIA A800-SXM4-80GB
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:213 - Total memory: 2015.3 GB
2025-08-02 20:07:26 - autocompose.system - INFO - log_system_info:214 - Available memory: 1909.3 GB
2025-08-02 20:07:26 - autocompose.main - INFO - __init__:216 - AutoComposeAgent initialized successfully
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:280 - Processing image: input.png (size: (1133, 753))
2025-08-02 20:07:26 - autocompose.main - INFO - _timed_step:239 - Step 1: Analyzing image composition...
2025-08-02 20:07:26 - autocompose.main - INFO - __enter__:233 - Starting: Perception Analysis
2025-08-02 20:07:26 - autocompose.agents.perception - ERROR - analyze_image:371 - llama-vision analysis failed: CUDA error: CUBLAS_STATUS_ALLOC_FAILED when calling `cublasCreate(handle)`
2025-08-02 20:07:26 - performance - INFO - end_timer:58 - Operation 'perception analysis' completed in 0.397s
2025-08-02 20:07:26 - autocompose.main - INFO - __exit__:239 - Completed: Perception Analysis (0.399s)
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:290 - Identified 0 composition issues: []
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:291 - Generated 0 recommendations
2025-08-02 20:07:26 - autocompose.main - INFO - _timed_step:239 - Step 2: Generating adjustment plan...
2025-08-02 20:07:26 - autocompose.main - INFO - __enter__:233 - Starting: Plan Generation
2025-08-02 20:07:26 - autocompose.agents.planning - ERROR - _query_local_qwen:331 - Local model query failed: CUDA error: CUBLAS_STATUS_ALLOC_FAILED when calling `cublasCreate(handle)`
2025-08-02 20:07:26 - performance - INFO - end_timer:58 - Operation 'plan generation' completed in 0.058s
2025-08-02 20:07:26 - autocompose.main - INFO - __exit__:239 - Completed: Plan Generation (0.058s)
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:298 - Generated plan with 1 instructions
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:299 - Reasoning: Minimal fallback plan - basic composition improvement
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:300 - Instructions: ['Crop the image to improve composition using center framing']
2025-08-02 20:07:26 - autocompose.main - INFO - _timed_step:239 - Step 3: Executing adjustment plan...
2025-08-02 20:07:26 - autocompose.main - INFO - __enter__:233 - Starting: Plan Execution
2025-08-02 20:07:26 - autocompose.main - INFO - process_image:312 - Using Planning Chain execution with feedback control
2025-08-02 20:07:26 - autocompose.agents.restoration - INFO - execute_plan:458 - Planning Chain enabled, using enhanced execution with feedback control
2025-08-02 20:07:26 - autocompose.agents.controller - INFO - __init__:259 - Planning Chain Controller initialized:
2025-08-02 20:07:26 - autocompose.agents.controller - INFO - __init__:260 -   - Rollback threshold: 0.0
2025-08-02 20:07:26 - autocompose.agents.controller - INFO - __init__:261 -   - Max local rollbacks: 2
2025-08-02 20:07:26 - autocompose.agents.controller - INFO - __init__:262 -   - Max global rollbacks: 1
2025-08-02 20:07:26 - autocompose.agents.controller - INFO - start_execution:270 - Started execution of plan with 1 steps
2025-08-02 20:07:26 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:487 - Starting Planning Chain execution with 1 instructions
2025-08-02 20:07:26 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:488 - Initial aesthetic score: 0.584
2025-08-02 20:07:26 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:501 - Executing step 1: Crop the image to improve composition using center framing
2025-08-02 20:07:27 - performance - INFO - end_timer:58 - Operation 'plan execution' completed in 0.796s
2025-08-02 20:07:27 - autocompose.main - INFO - __exit__:239 - Completed: Plan Execution (0.797s)
