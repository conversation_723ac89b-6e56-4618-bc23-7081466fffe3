#!/bin/bash
#SBATCH -p sail
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=16

source ~/.bashrc


export http_proxy=http://yaomingde.p:JBY7pAzQTroEvzT3FLFxiik7j8yfwwOCvdgROOXCOUvwFh8NKZ7aNhLeL5up@10.1.20.50:23128/
export https_proxy=http://yaomingde.p:JBY7pAzQTroEvzT3FLFxiik7j8yfwwOCvdgROOXCOUvwFh8NKZ7aNhLeL5up@10.1.20.50:23128/
export HTTP_PROXY=http://yaomingde.p:JBY7pAzQTroEvzT3FLFxiik7j8yfwwOCvdgROOXCOUvwFh8NKZ7aNhLeL5up@10.1.20.50:23128/
export HTTPS_PROXY=http://yaomingde.p:JBY7pAzQTroEvzT3FLFxiik7j8yfwwOCvdgROOXCOUvwFh8NKZ7aNhLeL5up@10.1.20.50:23128/


conda activate agent
cd /mnt/petrelfs/yaomingde.p/workspace/ideas/flux/code/4kagent/autocompose/agents/perception.py
python run.py process input.png output.png
