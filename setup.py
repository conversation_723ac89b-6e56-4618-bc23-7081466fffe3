"""
Setup script for AutoComposeAgent
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "AutoComposeAgent - Automatic Image Composition Adjustment System"

# Read requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="autocompose-agent",
    version="0.1.0",
    author="AutoComposeAgent Team",
    author_email="<EMAIL>",
    description="Automatic Image Composition Adjustment System using Multi-Agent Architecture",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/AutoComposeAgent",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Graphics",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
            "pre-commit>=2.20.0",
        ],
        "gpu": [
            "torch[cuda]",
            "xformers",
            "flash-attn",
        ],
        "full": [
            "jupyter>=1.0.0",
            "matplotlib>=3.6.0",
            "seaborn>=0.12.0",
            "plotly>=5.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "autocompose=run:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "autocompose": [
            "*.yaml",
            "*.yml",
            "*.json",
        ],
    },
    zip_safe=False,
    keywords=[
        "image processing",
        "composition",
        "aesthetic",
        "ai",
        "computer vision",
        "multi-agent",
        "automatic",
        "enhancement"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-repo/AutoComposeAgent/issues",
        "Source": "https://github.com/your-repo/AutoComposeAgent",
        "Documentation": "https://autocompose-agent.readthedocs.io/",
    },
)
