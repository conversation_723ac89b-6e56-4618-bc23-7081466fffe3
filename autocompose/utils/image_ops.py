"""
Image Operations Module

Implements various image manipulation operations for composition adjustment.
"""

import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import cv2
from typing import Tuple, Dict, Any, Optional, Union
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class ResampleMethod(Enum):
    """Resampling methods for image operations"""
    NEAREST = Image.NEAREST
    BILINEAR = Image.BILINEAR
    BICUBIC = Image.BICUBIC
    LANCZOS = Image.LANCZOS


class ImageOperations:
    """
    Collection of image manipulation operations for composition adjustment
    """
    
    @staticmethod
    def crop_image(image: Image.Image, parameters: Dict[str, Any]) -> Image.Image:
        """
        Crop image based on parameters

        Args:
            image: Input PIL Image
            parameters: Crop parameters (supports both old and new format)

        Returns:
            Cropped PIL Image
        """
        try:
            width, height = image.size

            # Handle new parameter format (aspect_ratio + method)
            if "aspect_ratio" in parameters:
                aspect_ratio = parameters.get("aspect_ratio", "16:9")
                method = parameters.get("method", "center")

                # Parse aspect ratio
                if ":" in aspect_ratio:
                    ratio_parts = aspect_ratio.split(":")
                    aspect_w, aspect_h = float(ratio_parts[0]), float(ratio_parts[1])
                    aspect_ratio_value = aspect_w / aspect_h
                else:
                    aspect_ratio_value = float(aspect_ratio)

                # Calculate target dimensions based on aspect ratio
                # Fit the aspect ratio within the current image dimensions
                if width / height > aspect_ratio_value:
                    # Image is wider than target aspect ratio
                    target_height = height
                    target_width = int(height * aspect_ratio_value)
                else:
                    # Image is taller than target aspect ratio
                    target_width = width
                    target_height = int(width / aspect_ratio_value)

                # Set crop method flags
                rule_of_thirds = (method == "rule_of_thirds")
                center_crop = (method == "center")

            else:
                # Handle old parameter format
                target_width = parameters.get("target_width", width)
                target_height = parameters.get("target_height", height)
                center_crop = parameters.get("center_crop", True)
                rule_of_thirds = parameters.get("rule_of_thirds", False)

            # Ensure target dimensions don't exceed original
            target_width = min(target_width, width)
            target_height = min(target_height, height)

            # Skip cropping if no change needed
            if target_width == width and target_height == height:
                logger.debug("No cropping needed - target size matches original")
                return image

            # Calculate crop box
            if rule_of_thirds:
                # Rule of thirds crop - position subject at intersection
                # Use the upper-left intersection point for better composition
                left = int(width * 1/3 - target_width // 2)
                top = int(height * 1/3 - target_height // 2)
            elif center_crop:
                # Center crop
                left = (width - target_width) // 2
                top = (height - target_height) // 2
            else:
                # Default to top-left
                left = 0
                top = 0

            # Ensure crop box is within image bounds
            left = max(0, min(left, width - target_width))
            top = max(0, min(top, height - target_height))
            right = left + target_width
            bottom = top + target_height

            # Ensure crop box coordinates are integers
            box = (int(left), int(top), int(right), int(bottom))

            # Perform crop
            cropped = image.crop(box)

            logger.info(f"Cropped image from {width}x{height} to {target_width}x{target_height} using method: {parameters.get('method', 'center')}")
            return cropped

        except Exception as e:
            logger.error(f"Crop operation failed: {e}")
            return image
    
    @staticmethod
    def rotate_image(image: Image.Image, parameters: Dict[str, Any]) -> Image.Image:
        """
        Rotate image based on parameters
        
        Args:
            image: Input PIL Image
            parameters: Rotation parameters
            
        Returns:
            Rotated PIL Image
        """
        try:
            angle = parameters.get("angle", 0)
            expand = parameters.get("expand", True)
            fill_color = parameters.get("fill_color", (255, 255, 255))
            resample_method = parameters.get("resample", "bicubic")
            
            # Convert resample method
            if isinstance(resample_method, str):
                resample_map = {
                    "nearest": Image.NEAREST,
                    "bilinear": Image.BILINEAR,
                    "bicubic": Image.BICUBIC,
                    "lanczos": Image.LANCZOS
                }
                resample = resample_map.get(resample_method.lower(), Image.BICUBIC)
            else:
                resample = resample_method
            
            # Ensure angle is a float
            angle = float(angle)
            
            # Perform rotation
            rotated = image.rotate(
                angle=angle,
                resample=resample,
                expand=expand,
                fillcolor=fill_color
            )
            
            logger.debug(f"Rotated image by {angle} degrees")
            return rotated
            
        except Exception as e:
            logger.error(f"Rotation operation failed: {e}")
            return image
    
    @staticmethod
    def recenter_image(image: Image.Image, parameters: Dict[str, Any]) -> Image.Image:
        """
        Recenter image based on parameters
        
        Args:
            image: Input PIL Image
            parameters: Recentering parameters
            
        Returns:
            Recentered PIL Image
        """
        try:
            width, height = image.size
            
            method = parameters.get("method", "center_bias")
            target_x = parameters.get("target_x", width // 2)
            target_y = parameters.get("target_y", height // 2)
            crop_size = parameters.get("crop_size", (int(width * 0.8), int(height * 0.8)))
            
            crop_width, crop_height = crop_size
            
            # Calculate crop box centered on target point
            left = int(target_x - crop_width // 2)
            top = int(target_y - crop_height // 2)
            
            # Ensure crop box is within image bounds
            left = max(0, min(left, width - crop_width))
            top = max(0, min(top, height - crop_height))
            right = left + crop_width
            bottom = top + crop_height
            
            # Ensure crop box coordinates are integers
            box = (int(left), int(top), int(right), int(bottom))
            
            # Perform crop
            recentered = image.crop(box)
            
            logger.debug(f"Recentered image using {method} method")
            return recentered
            
        except Exception as e:
            logger.error(f"Recentering operation failed: {e}")
            return image
    
    @staticmethod
    def adjust_colors(image: Image.Image, parameters: Dict[str, Any]) -> Image.Image:
        """
        Adjust image colors based on parameters
        
        Args:
            image: Input PIL Image
            parameters: Color adjustment parameters
            
        Returns:
            Color-adjusted PIL Image
        """
        try:
            brightness = parameters.get("brightness", 0)
            contrast = parameters.get("contrast", 1.0)
            saturation = parameters.get("saturation", 1.0)
            preserve_luminance = parameters.get("preserve_luminance", True)
            
            adjusted = image.copy()
            
            # Apply brightness adjustment
            if brightness != 0:
                enhancer = ImageEnhance.Brightness(adjusted)
                adjusted = enhancer.enhance(1.0 + brightness)
            
            # Apply contrast adjustment
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(adjusted)
                adjusted = enhancer.enhance(contrast)
            
            # Apply saturation adjustment
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(adjusted)
                adjusted = enhancer.enhance(saturation)
            
            logger.debug(f"Adjusted colors: brightness={brightness}, contrast={contrast}, saturation={saturation}")
            return adjusted
            
        except Exception as e:
            logger.error(f"Color adjustment operation failed: {e}")
            return image
    
    @staticmethod
    def adjust_brightness(image: Image.Image, parameters: Dict[str, Any]) -> Image.Image:
        """
        Adjust image brightness based on parameters
        
        Args:
            image: Input PIL Image
            parameters: Brightness adjustment parameters
            
        Returns:
            Brightness-adjusted PIL Image
        """
        try:
            brightness_factor = parameters.get("brightness_factor", 1.0)
            
            enhancer = ImageEnhance.Brightness(image)
            adjusted = enhancer.enhance(brightness_factor)
            
            logger.debug(f"Adjusted brightness by factor {brightness_factor}")
            return adjusted
            
        except Exception as e:
            logger.error(f"Brightness adjustment operation failed: {e}")
            return image
    
    @staticmethod
    def apply_filter(image: Image.Image, filter_type: str, **kwargs) -> Image.Image:
        """
        Apply image filter
        
        Args:
            image: Input PIL Image
            filter_type: Type of filter to apply
            **kwargs: Filter-specific parameters
            
        Returns:
            Filtered PIL Image
        """
        try:
            if filter_type == "blur":
                radius = kwargs.get("radius", 1)
                return image.filter(ImageFilter.GaussianBlur(radius=radius))
            
            elif filter_type == "sharpen":
                return image.filter(ImageFilter.SHARPEN)
            
            elif filter_type == "edge_enhance":
                return image.filter(ImageFilter.EDGE_ENHANCE)
            
            elif filter_type == "smooth":
                return image.filter(ImageFilter.SMOOTH)
            
            else:
                logger.warning(f"Unknown filter type: {filter_type}")
                return image
                
        except Exception as e:
            logger.error(f"Filter operation failed: {e}")
            return image
    
    @staticmethod
    def resize_image(image: Image.Image, size: Tuple[int, int], 
                    resample: ResampleMethod = ResampleMethod.LANCZOS) -> Image.Image:
        """
        Resize image to specified size
        
        Args:
            image: Input PIL Image
            size: Target size (width, height)
            resample: Resampling method
            
        Returns:
            Resized PIL Image
        """
        try:
            # Ensure size dimensions are integers
            size = (int(size[0]), int(size[1]))
            resized = image.resize(size, resample.value)
            logger.debug(f"Resized image to {size}")
            return resized
            
        except Exception as e:
            logger.error(f"Resize operation failed: {e}")
            return image
    
    @staticmethod
    def get_image_stats(image: Image.Image) -> Dict[str, Any]:
        """
        Get statistical information about an image
        
        Args:
            image: Input PIL Image
            
        Returns:
            Dictionary of image statistics
        """
        try:
            # Convert to numpy array for analysis
            img_array = np.array(image)
            
            stats = {
                "size": image.size,
                "mode": image.mode,
                "channels": len(img_array.shape) if len(img_array.shape) > 2 else 1,
                "mean_brightness": np.mean(img_array),
                "std_brightness": np.std(img_array),
                "min_value": np.min(img_array),
                "max_value": np.max(img_array)
            }
            
            # Calculate histogram
            if image.mode == "RGB":
                stats["histogram"] = {
                    "red": np.histogram(img_array[:, :, 0], bins=256, range=(0, 256))[0].tolist(),
                    "green": np.histogram(img_array[:, :, 1], bins=256, range=(0, 256))[0].tolist(),
                    "blue": np.histogram(img_array[:, :, 2], bins=256, range=(0, 256))[0].tolist()
                }
            else:
                stats["histogram"] = np.histogram(img_array, bins=256, range=(0, 256))[0].tolist()
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get image stats: {e}")
            return {"size": image.size, "mode": image.mode}
    
    @staticmethod
    def detect_edges(image: Image.Image, threshold1: int = 100, 
                    threshold2: int = 200) -> Image.Image:
        """
        Detect edges in image using Canny edge detection
        
        Args:
            image: Input PIL Image
            threshold1: First threshold for edge detection
            threshold2: Second threshold for edge detection
            
        Returns:
            Edge-detected PIL Image
        """
        try:
            # Convert to grayscale and numpy array
            gray = image.convert("L")
            img_array = np.array(gray)
            
            # Apply Canny edge detection
            edges = cv2.Canny(img_array, threshold1, threshold2)
            
            # Convert back to PIL Image
            edge_image = Image.fromarray(edges, mode="L")
            
            logger.debug("Applied edge detection")
            return edge_image
            
        except Exception as e:
            logger.error(f"Edge detection failed: {e}")
            return image.convert("L")
    
    @staticmethod
    def calculate_sharpness(image: Image.Image) -> float:
        """
        Calculate image sharpness using Laplacian variance

        Args:
            image: Input PIL Image

        Returns:
            Sharpness score (higher is sharper)
        """
        try:
            # Convert to grayscale if needed
            if image.mode != 'L':
                gray_image = image.convert('L')
            else:
                gray_image = image

            # Convert to numpy array
            np_image = np.array(gray_image)

            # Apply Laplacian filter
            laplacian = cv2.Laplacian(np_image, cv2.CV_64F)

            # Calculate variance (higher variance = sharper image)
            variance = laplacian.var()

            # Normalize to 0-1 range (approximate)
            normalized_sharpness = min(1.0, variance / 1000.0)

            return normalized_sharpness

        except Exception as e:
            logger.warning(f"Sharpness calculation failed: {e}")
            return 0.5  # Default neutral value

    @staticmethod
    def calculate_composition_score(image: Image.Image) -> Dict[str, float]:
        """
        Calculate basic composition scores for an image

        Args:
            image: Input PIL Image

        Returns:
            Dictionary of composition scores
        """
        try:
            width, height = image.size
            img_array = np.array(image.convert("L"))  # Convert to grayscale

            scores = {}

            # Rule of thirds score - improved calculation
            # Check if interesting content is near rule of thirds lines and intersections
            thirds_x = [width // 3, 2 * width // 3]
            thirds_y = [height // 3, 2 * height // 3]

            # Calculate variance near rule of thirds lines and intersections
            thirds_variance = 0
            intersection_variance = 0

            # Variance along thirds lines
            for x in thirds_x:
                if x < width:
                    line_region = img_array[:, max(0, x-5):min(width, x+5)]
                    thirds_variance += np.var(line_region)
            for y in thirds_y:
                if y < height:
                    line_region = img_array[max(0, y-5):min(height, y+5), :]
                    thirds_variance += np.var(line_region)

            # Variance at intersection points (more important)
            for x in thirds_x:
                for y in thirds_y:
                    if x < width and y < height:
                        intersection_region = img_array[
                            max(0, y-15):min(height, y+15),
                            max(0, x-15):min(width, x+15)
                        ]
                        intersection_variance += np.var(intersection_region)

            # Normalize thirds score with emphasis on intersections
            total_variance = np.var(img_array)
            if total_variance > 0:
                line_score = thirds_variance / (total_variance * 4)  # 4 lines
                intersection_score = intersection_variance / (total_variance * 4)  # 4 intersections
                scores["rule_of_thirds"] = min(1.0, 0.3 * line_score + 0.7 * intersection_score)
            else:
                scores["rule_of_thirds"] = 0.0

            # Aspect ratio score - reward common good ratios
            aspect_ratio = width / height
            good_ratios = [16/9, 4/3, 3/2, 5/4, 1.618]  # Including golden ratio
            aspect_score = 0.0
            for ratio in good_ratios:
                ratio_diff = abs(aspect_ratio - ratio) / ratio
                if ratio_diff < 0.1:  # Within 10% of good ratio
                    aspect_score = max(aspect_score, 1.0 - ratio_diff * 5)
            scores["aspect_ratio"] = aspect_score

            # Balance score (symmetry) - improved
            left_half = img_array[:, :width//2]
            right_half = img_array[:, width//2:]

            if right_half.shape[1] > 0:
                right_half_flipped = np.fliplr(right_half)

                # Resize to match if needed
                min_width = min(left_half.shape[1], right_half_flipped.shape[1])
                left_half = left_half[:, :min_width]
                right_half_flipped = right_half_flipped[:, :min_width]

                balance_diff = np.mean(np.abs(left_half.astype(float) - right_half_flipped.astype(float)))
                scores["balance"] = max(0.0, 1.0 - balance_diff / 128.0)  # More sensitive
            else:
                scores["balance"] = 0.5

            # Contrast score - improved
            contrast = np.std(img_array)
            scores["contrast"] = min(1.0, contrast / 64.0)  # Normalize to reasonable range

            # Edge density score (complexity) - improved
            edges = cv2.Canny(img_array, 50, 150)
            edge_density = np.sum(edges > 0) / (width * height)
            # Optimal edge density is around 0.05-0.15
            if edge_density < 0.05:
                scores["edge_density"] = edge_density * 10  # Too few edges
            elif edge_density > 0.15:
                scores["edge_density"] = max(0.0, 1.0 - (edge_density - 0.15) * 3)  # Too many edges
            else:
                scores["edge_density"] = 0.75 + (edge_density - 0.1) * 2.5  # Optimal range

            # Overall composition score with updated weights
            weights = {
                "rule_of_thirds": 0.35,
                "aspect_ratio": 0.15,
                "balance": 0.15,
                "contrast": 0.20,
                "edge_density": 0.15
            }

            scores["overall"] = sum(scores[key] * weights[key] for key in weights)

            # Add some randomness to prevent identical scores
            scores["overall"] += np.random.normal(0, 0.01)
            scores["overall"] = max(0.0, min(1.0, scores["overall"]))

            return scores

        except Exception as e:
            logger.error(f"Composition score calculation failed: {e}")
            return {"overall": 0.5}
    
    @staticmethod
    def create_comparison_grid(images: list, labels: Optional[list] = None, 
                             grid_size: Optional[Tuple[int, int]] = None) -> Image.Image:
        """
        Create a comparison grid of images
        
        Args:
            images: List of PIL Images
            labels: Optional list of labels for images
            grid_size: Optional grid size (cols, rows)
            
        Returns:
            Grid image as PIL Image
        """
        try:
            if not images:
                raise ValueError("No images provided")
            
            num_images = len(images)
            
            # Determine grid size
            if grid_size is None:
                cols = int(np.ceil(np.sqrt(num_images)))
                rows = int(np.ceil(num_images / cols))
            else:
                cols, rows = grid_size
            
            # Get maximum dimensions
            max_width = max(img.size[0] for img in images)
            max_height = max(img.size[1] for img in images)
            
            # Create grid image
            grid_width = cols * max_width
            grid_height = rows * max_height
            grid_image = Image.new("RGB", (grid_width, grid_height), (255, 255, 255))
            
            # Place images in grid
            for i, img in enumerate(images):
                row = i // cols
                col = i % cols
                
                x = col * max_width
                y = row * max_height
                
                # Resize image to fit grid cell
                img_resized = img.resize((max_width, max_height), Image.LANCZOS)
                grid_image.paste(img_resized, (x, y))
            
            logger.debug(f"Created comparison grid with {num_images} images")
            return grid_image
            
        except Exception as e:
            logger.error(f"Grid creation failed: {e}")
            # Return first image as fallback
            return images[0] if images else Image.new("RGB", (100, 100), (255, 255, 255))
