"""
Environment utilities for AutoComposeAgent

Provides utilities for cleaning environment variables and setting up CUDA.
"""



import os  # 导入操作系统接口模块，用于环境变量操作
import logging  # 导入日志记录模块
from pathlib import Path  # 导入路径处理模块，提供面向对象的路径操作
from typing import Dict, List, Optional  # 导入类型注解模块

logger = logging.getLogger(__name__)  # 创建当前模块的日志记录器


def clean_environment_paths():
    """
    Clean environment variables that contain invalid paths
    
    This function addresses issues where environment variables contain
    malformed URLs, invalid paths, or other problematic values that
    can cause warnings in libraries like bitsandbytes.
    """
    # Environment variables that commonly contain paths
    # 定义常见的包含路径信息的环境变量列表
    path_env_vars = [
        'PATH',           # 系统可执行文件搜索路径
        'LD_LIBRARY_PATH',  # 动态链接库搜索路径
        'LIBRARY_PATH',     # 编译时库搜索路径
        'CUDA_PATH',        # CUDA安装路径
        'CUDA_HOME',        # CUDA主目录路径
        'CUDNN_PATH',       # cuDNN库路径
        'TENSORRT_PATH'     # TensorRT库路径
    ]
    
    cleaned_vars = []  # 初始化已清理的环境变量名称列表
    
    # 遍历每个需要检查的环境变量
    for env_var in path_env_vars:
        if env_var in os.environ:  # 检查环境变量是否存在
            original_value = os.environ[env_var]  # 获取原始环境变量值
            cleaned_value = clean_path_string(original_value)  # 清理路径字符串
            
            if cleaned_value != original_value:  # 如果清理后的值与原始值不同
                os.environ[env_var] = cleaned_value  # 更新环境变量为清理后的值
                cleaned_vars.append(env_var)  # 将变量名添加到已清理列表
                logger.debug(f"Cleaned {env_var}: removed invalid paths")  # 记录调试信息
    
    if cleaned_vars:  # 如果有环境变量被清理
        logger.info(f"Cleaned environment variables: {', '.join(cleaned_vars)}")  # 记录信息日志
    
    return cleaned_vars  # 返回已清理的环境变量名称列表


def clean_path_string(path_string: str) -> str:
    """
    Clean a path string by removing invalid path components
    
    Args:
        path_string: Colon-separated path string (like PATH or LD_LIBRARY_PATH)
        
    Returns:
        Cleaned path string with invalid components removed
    """
    if not path_string:  # 如果路径字符串为空或None
        return path_string  # 直接返回原字符串
    
    # 使用列表推导式简化逻辑
    # 分割路径字符串（以冒号分隔），过滤出有效的路径组件
    valid_paths = [path for path in path_string.split(':')  # 按冒号分割路径字符串
                  if path.strip() and is_valid_path_component(path.strip())]  # 检查路径是否非空且有效
    
    return ':'.join(valid_paths)  # 将有效路径重新用冒号连接并返回


def is_valid_path_component(path: str) -> bool:
    """
    Check if a path component is valid
    
    Args:
        path: Path component to validate
        
    Returns:
        True if the path component appears valid
    """
    # 简化条件检查
    if not path or not path.strip():  # 如果路径为空或只包含空白字符
        return False  # 返回False表示无效
    
    # 无效的模式列表
    # 定义各种无效路径模式的列表
    invalid_patterns = [
        'http://',     # HTTP协议前缀（不应出现在PATH中）
        'https://',    # HTTPS协议前缀
        'ftp://',      # FTP协议前缀
        '@',           # 邮箱符号（通常表示格式错误）
        '://',         # 通用协议分隔符
        'JBY7pAzQTroEvzT3FLFxiik7j8yfwwOCvdgROOXCOUvwFh8NKZ7aNhLeL5up'  # 特定的错误字符串
    ]
    
    # 简化条件检查
    # 使用逻辑非操作符检查路径是否无效
    return not (
        any(pattern in path for pattern in invalid_patterns) or  # 检查是否包含任何无效模式
        path.isdigit() or  # 检查是否为纯数字（通常不是有效路径）
        (path.endswith('.sock') and not Path(path).exists()) or  # 检查socket文件是否存在
        path.startswith('() {') or 'eval' in path  # 检查是否包含shell函数或eval命令
    )


def setup_cuda_environment():
    """
    Setup CUDA environment variables to prevent bitsandbytes issues
    """
    # 如果CUDA_HOME未设置且CUDA可用，则设置它
    if 'CUDA_HOME' not in os.environ:  # 检查CUDA_HOME环境变量是否未设置
        # 定义常见的CUDA安装路径列表
        cuda_paths = [
            '/usr/local/cuda',      # 默认CUDA安装路径
            '/opt/cuda',            # 可选CUDA安装路径
            '/usr/local/cuda-12.1', # CUDA 12.1特定版本路径
            '/usr/local/cuda-12.4', # CUDA 12.4特定版本路径
            '/usr/local/cuda-11.8'  # CUDA 11.8特定版本路径
        ]
        
        # 使用next+生成器表达式查找第一个存在的CUDA路径
        # 遍历CUDA路径列表，找到第一个实际存在的路径
        cuda_path = next((path for path in cuda_paths if Path(path).exists()), None)
        if cuda_path:  # 如果找到有效的CUDA路径
            os.environ['CUDA_HOME'] = cuda_path  # 设置CUDA_HOME环境变量
            logger.info(f"Set CUDA_HOME to {cuda_path}")  # 记录设置信息
    
    # 确保CUDA库路径在LD_LIBRARY_PATH中
    cuda_home = os.environ.get('CUDA_HOME')  # 获取CUDA_HOME环境变量值
    if cuda_home:  # 如果CUDA_HOME已设置
        cuda_lib_path = f"{cuda_home}/lib64"  # 构造CUDA库文件路径
        if Path(cuda_lib_path).exists():  # 检查CUDA库路径是否存在
            ld_library_path = os.environ.get('LD_LIBRARY_PATH', '')  # 获取当前LD_LIBRARY_PATH值
            if cuda_lib_path not in ld_library_path:  # 如果CUDA库路径不在LD_LIBRARY_PATH中
                # 将CUDA库路径添加到LD_LIBRARY_PATH的开头
                os.environ['LD_LIBRARY_PATH'] = f"{cuda_lib_path}:{ld_library_path}" if ld_library_path else cuda_lib_path
                logger.info(f"Added {cuda_lib_path} to LD_LIBRARY_PATH")  # 记录添加信息


def validate_environment():
    """
    Validate and clean the environment before model loading
    
    This should be called early in the application startup to prevent
    issues with libraries that parse environment variables.
    """
    logger.info("Validating and cleaning environment...")  # 记录环境验证开始信息
    
    # 清理路径变量
    cleaned_vars = clean_environment_paths()  # 调用函数清理环境变量路径
    
    # 设置CUDA环境
    setup_cuda_environment()  # 调用函数设置CUDA环境变量
    
    # 记录环境状态
    cuda_available = 'CUDA_HOME' in os.environ  # 检查CUDA是否可用（通过CUDA_HOME判断）
    logger.info(f"Environment validation complete. CUDA setup: {'✓' if cuda_available else '✗'}")  # 记录验证完成信息
    
    # 返回环境验证结果字典
    return {
        'cleaned_variables': cleaned_vars,  # 已清理的环境变量列表
        'cuda_available': cuda_available    # CUDA是否可用的布尔值
    }


def suppress_bitsandbytes_warnings():
    """
    Suppress known bitsandbytes warnings that don't affect functionality
    """
    import warnings  # 导入警告模块
    
    # 使用列表简化警告过滤
    # 定义需要抑制的警告信息模式列表
    warning_patterns = [
        '.*libcudart.so.*',  # 匹配libcudart.so相关的警告
        '.*CUDA_SETUP.*',    # 匹配CUDA_SETUP相关的警告
        '.*non-existent.*'   # 匹配关于不存在文件的警告
    ]
    
    # 遍历警告模式并设置过滤器
    for pattern in warning_patterns:
        warnings.filterwarnings('ignore', message=pattern)  # 忽略匹配模式的警告信息
    
    logger.debug("Suppressed bitsandbytes warnings")  # 记录调试信息，表明已抑制警告
