"""
Logging Configuration Module

Provides centralized logging configuration for AutoComposeAgent.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import time
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels"""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class PerformanceLogger:
    """Logger for performance metrics and timing"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
        self.timers = {}
    
    def start_timer(self, operation: str) -> None:
        """Start timing an operation"""
        self.timers[operation] = time.time()
        self.logger.debug(f"Started timing: {operation}")
    
    def end_timer(self, operation: str) -> float:
        """End timing an operation and log the duration"""
        if operation not in self.timers:
            self.logger.warning(f"Timer for '{operation}' was not started")
            return 0.0
        
        duration = time.time() - self.timers[operation]
        del self.timers[operation]
        
        self.logger.info(f"Operation '{operation}' completed in {duration:.3f}s")
        return duration
    
    def log_memory_usage(self, operation: str = "current") -> None:
        """Log current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            self.logger.debug(f"Memory usage ({operation}): {memory_mb:.1f} MB")
        except ImportError:
            self.logger.debug("psutil not available for memory monitoring")


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: Optional[str] = None,
    enable_colors: bool = True,
    enable_performance_logging: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> Dict[str, logging.Logger]:
    """
    Setup comprehensive logging configuration
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Specific log file name
        log_dir: Directory for log files
        enable_colors: Whether to enable colored console output
        enable_performance_logging: Whether to enable performance logging
        max_file_size: Maximum size of log files before rotation
        backup_count: Number of backup files to keep
        
    Returns:
        Dictionary of configured loggers
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level, logging.INFO)
    
    # Create log directory if specified
    if log_dir:
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
    else:
        log_path = Path("logs")
        log_path.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    
    if enable_colors and sys.stdout.isatty():
        console_format = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
    else:
        console_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
    
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        file_path = log_path / log_file
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = log_path / f"autocompose_{timestamp}.log"
    
    file_handler = logging.handlers.RotatingFileHandler(
        file_path,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)  # Always log everything to file
    
    file_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)
    
    # Create specific loggers
    loggers = {
        'main': logging.getLogger('autocompose.main'),
        'perception': logging.getLogger('autocompose.agents.perception'),
        'planning': logging.getLogger('autocompose.agents.planning'),
        'restoration': logging.getLogger('autocompose.agents.restoration'),
        'evaluator': logging.getLogger('autocompose.core.evaluator'),
        'profile': logging.getLogger('autocompose.core.profile'),
        'image_ops': logging.getLogger('autocompose.utils.image_ops')
    }
    
    # Performance logger
    if enable_performance_logging:
        perf_logger = PerformanceLogger()
        loggers['performance'] = perf_logger
    
    # Log the setup
    main_logger = loggers['main']
    main_logger.info(f"Logging initialized - Level: {level}, File: {file_path}")
    
    return loggers


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name"""
    return logging.getLogger(f"autocompose.{name}")


def log_system_info():
    """Log system information for debugging"""
    logger = get_logger("system")
    
    try:
        import platform
        import sys
        
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Platform: {platform.platform()}")
        logger.info(f"Architecture: {platform.architecture()}")
        
        # GPU information
        try:
            import torch
            logger.info(f"PyTorch version: {torch.__version__}")
            logger.info(f"CUDA available: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                logger.info(f"CUDA version: {torch.version.cuda}")
                logger.info(f"GPU count: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    gpu_name = torch.cuda.get_device_name(i)
                    logger.info(f"GPU {i}: {gpu_name}")
        except ImportError:
            logger.info("PyTorch not available")
        
        # Memory information
        try:
            import psutil
            memory = psutil.virtual_memory()
            logger.info(f"Total memory: {memory.total / 1024**3:.1f} GB")
            logger.info(f"Available memory: {memory.available / 1024**3:.1f} GB")
        except ImportError:
            logger.info("psutil not available for memory info")
            
    except Exception as e:
        logger.error(f"Failed to log system info: {e}")


class LogContext:
    """Context manager for logging operations with timing"""
    
    def __init__(self, logger: logging.Logger, operation: str, level: int = logging.INFO):
        self.logger = logger
        self.operation = operation
        self.level = level
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.log(self.level, f"Starting: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        if exc_type is None:
            self.logger.log(self.level, f"Completed: {self.operation} ({duration:.3f}s)")
        else:
            self.logger.error(f"Failed: {self.operation} ({duration:.3f}s) - {exc_val}")
