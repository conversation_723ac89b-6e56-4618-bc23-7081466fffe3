"""
Main AutoComposeAgent System

Integrates all agents to provide complete image composition adjustment workflow.
"""

# 设置 CUDA 确定性行为的环境变量（必须在导入 torch 之前设置）
import os  # 导入操作系统接口模块
if 'CUBLAS_WORKSPACE_CONFIG' not in os.environ:  # 检查环境变量是否已设置
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'  # 设置CUDA库的工作空间配置，确保计算结果的确定性
import logging  # 导入日志记录模块
from typing import Dict, List, Any, Optional, Union  # 导入类型提示
from PIL import Image  # 导入PIL图像处理库
from pathlib import Path  # 导入路径操作库
import time  # 导入时间模块
import contextlib  # 导入上下文管理器模块

from .core.profile import Profile  # 导入配置管理模块
from .agents.perception import PerceptionAgent  # 导入感知代理
from .agents.planning import PlanningAgent  # 导入规划代理
from .agents.restoration import RestorationAgent  # 导入修复代理
from .utils.image_ops import ImageOperations  # 导入图像操作工具类
from .utils.logging_config import setup_logging, get_logger, log_system_info, LogContext  # 导入日志配置工具

logger = logging.getLogger(__name__)  # 获取当前模块的日志记录器


class AutoComposeAgent:
    """
    Main AutoComposeAgent system that coordinates all agents
    主要的AutoComposeAgent系统，协调所有代理
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize AutoComposeAgent system
        初始化AutoComposeAgent系统

        Args:
            config_path: Path to configuration file 配置文件路径
        """
        # Clean environment before loading models
        # 在加载模型之前清理环境
        from .utils.env_utils import validate_environment, suppress_bitsandbytes_warnings  # 导入环境工具
        validate_environment()  # 验证环境配置
        suppress_bitsandbytes_warnings()  # 抑制bitsandbytes库的警告信息

        # Load configuration
        # 加载配置
        config_path = config_path or 'config.yaml'  # 使用提供的配置路径或默认配置文件
        self.profile = Profile(config_path)  # 创建配置文件实例

        # Initialize agents
        # 初始化代理
        self.perception_agent = PerceptionAgent(self.profile)  # 创建感知代理实例
        logger.info("PerceptionAgent initialized successfully")  # 记录感知代理初始化成功

        self.planning_agent = PlanningAgent(self.profile)  # 创建规划代理实例
        logger.info("PlanningAgent initialized successfully")  # 记录规划代理初始化成功

        self.restoration_agent = RestorationAgent(self.profile)  # 创建修复代理实例
        logger.info("RestorationAgent initialized successfully")  # 记录修复代理初始化成功

        # Setup logging
        # 设置日志系统
        self._setup_logging()  # 调用日志设置函数

        logger.info("AutoComposeAgent initialized successfully")  # 记录AutoComposeAgent初始化成功
    
    def _setup_logging(self):
        """Setup comprehensive logging configuration 设置全面的日志配置"""
        if self.profile.logging:  # 如果配置中启用了日志
            loggers = setup_logging(  # 设置日志系统
                level=self.profile.logging.level,  # 设置日志级别
                enable_colors=True,  # 启用彩色日志
                enable_performance_logging=True  # 启用性能日志
            )
            log_system_info()  # 记录系统信息
            self.performance_logger = loggers.get('performance')  # 获取性能日志记录器
        else:
            logging.basicConfig(  # 基础日志配置
                level=logging.INFO,  # 设置日志级别为INFO
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'  # 设置日志格式
            )
            self.performance_logger = None  # 没有性能日志记录器

    @contextlib.contextmanager  # 上下文管理器装饰器
    def _timed_step(self, step_name, context_msg=None):
        """Context manager for timing and logging a processing step 用于计时和记录处理步骤的上下文管理器"""
        if context_msg:  # 如果有上下文消息
            logger.info(f"Step {context_msg}")  # 记录步骤信息
        
        context = LogContext(logger, step_name)  # 创建日志上下文
        context.__enter__()  # 进入上下文
        
        if self.performance_logger:  # 如果有性能日志记录器
            self.performance_logger.start_timer(step_name.lower())  # 开始计时
        
        try:
            yield  # 执行被管理的代码块
        finally:
            if self.performance_logger:  # 如果有性能日志记录器
                self.performance_logger.end_timer(step_name.lower())  # 结束计时
                self.performance_logger.log_memory_usage(f"after_{step_name.lower()}")  # 记录内存使用情况
            
            context.__exit__(None, None, None)  # 退出上下文
    
    def process_image(self, input_path: Union[str, Path], 
                     output_path: Optional[Union[str, Path]] = None,
                     save_intermediate: Optional[bool] = None) -> Dict[str, Any]:
        """
        Process a single image through the complete workflow
        通过完整的工作流处理单张图像
        
        Args:
            input_path: Path to input image 输入图像路径
            output_path: Path to save output image 输出图像保存路径
            save_intermediate: Whether to save intermediate results 是否保存中间结果
            
        Returns:
            Processing results dictionary 处理结果字典
        """
        start_time = time.time()  # 记录开始时间
        
        # Load image
        # 加载图像
        input_path = Path(input_path)  # 转换为Path对象
        if not input_path.exists():  # 检查文件是否存在
            raise FileNotFoundError(f"Input image not found: {input_path}")  # 抛出文件未找到异常
        
        image = Image.open(input_path).convert("RGB")  # 打开图像并转换为RGB格式
        logger.info(f"Processing image: {input_path} (size: {image.size})")  # 记录正在处理的图像信息
        
        # Step 1: Perception - Analyze image
        # 步骤1：感知 - 分析图像
        with self._timed_step("Perception Analysis", "1: Analyzing image composition..."):  # 计时上下文管理器
            perception_result = self.perception_agent.analyze_image(image)  # 调用感知代理分析图像

            # print('perception_result',perception_result)  # 调试输出（已注释）
            # exit()  # 调试退出（已注释）

        logger.info(f"Identified {len(perception_result.composition_issues)} composition issues: {perception_result.composition_issues}")  # 记录识别的构图问题
        logger.info(f"Generated {len(perception_result.recommendations)} recommendations")  # 记录生成的建议数量
        
        # Step 2: Planning - Generate adjustment plan
        # 步骤2：规划 - 生成调整计划
        with self._timed_step("Plan Generation", "2: Generating adjustment plan..."):  # 计时上下文管理器
            adjustment_plan = self.planning_agent.generate_plan(perception_result, image)  # 调用规划代理生成计划

        logger.info(f"Generated plan with {len(adjustment_plan.instructions)} instructions")  # 记录生成的计划指令数量

        logger.info(f"Instructions: {[inst.instruction for inst in adjustment_plan.instructions]}")  # 记录具体指令

        # Step 3: Restoration - Execute plan
        # 步骤3：修复 - 执行计划
        with self._timed_step("Plan Execution", "3: Executing adjustment plan..."):  # 计时上下文管理器
            # 检查是否启用 Planning Chain
            # 检查是否启用规划链
            enable_planning_chain = getattr(self.profile.profile, 'enable_planning_chain', False) if self.profile.profile else False  # 获取规划链启用状态

            if enable_planning_chain:  # 如果启用了规划链
                # 使用 Planning Chain 执行
                # 使用规划链执行
                logger.info("Using Planning Chain execution with feedback control")  # 记录使用规划链执行
                restoration_result = self.restoration_agent.execute_plan(  # 执行计划
                    image, adjustment_plan, perception_result, self.planning_agent
                )
            else:
                # 使用标准执行
                # 使用标准执行
                restoration_result = self.restoration_agent.execute_plan(image, adjustment_plan)  # 执行计划
        
        logger.info(f"Final aesthetic score: {restoration_result.final_score:.3f} (Improvement: {restoration_result.total_improvement:.3f})")  # 记录最终美学评分

        # 计算成功率
        success_rate = sum(1 for result in restoration_result.operation_results if result.success) / len(restoration_result.operation_results) if restoration_result.operation_results else 0
        logger.info(f"Success rate: {success_rate:.1%}")  # 记录成功率

        # 简化的规划链信息（删除了不存在的字段）
        planning_chain_info = {"enabled": False, "execution_method": "standard"}  # 默认未启用

        # Prepare results
        # 准备结果
        results = {  # 构建结果字典
            "input_path": str(input_path),  # 输入路径
            "processing_time": time.time() - start_time,  # 处理时间
            "perception": {  # 感知结果
                "content_description": perception_result.content_description,  # 内容描述
                "composition_issues": perception_result.composition_issues,  # 构图问题
                "issue_count": len(perception_result.composition_issues),  # 问题数量
                "recommendations": perception_result.recommendations,  # 建议
                "initial_score": getattr(restoration_result, 'original_score', 0.0)  # 初始评分（从修复结果获取）
            },
            "planning": {  # 规划结果
                "num_instructions": len(adjustment_plan.instructions),  # 指令数量
                "instructions": [inst.instruction for inst in adjustment_plan.instructions],  # 指令列表
                "planning_chain": planning_chain_info  # 规划链信息
            },
            "restoration": {  # 修复结果
                "final_score": restoration_result.final_score,  # 最终评分
                "actual_improvement": restoration_result.total_improvement,  # 实际改进
                "success_rate": success_rate,  # 成功率（计算得出）
                "execution_time": restoration_result.processing_time,  # 执行时间
                "planning_chain": planning_chain_info  # 规划链信息
            }
        }
        
        # Save output image if path provided
        # 如果提供了路径则保存输出图像
        if output_path:  # 如果有输出路径
            self._save_output_image(output_path, restoration_result.final_image, results)  # 保存输出图像
            
            # Handle intermediate results if needed
            # 如果需要则处理中间结果
            should_save_intermediate = save_intermediate  # 获取是否保存中间结果的设置
            if should_save_intermediate is None:  # 如果没有明确设置
                should_save_intermediate = self.profile.logging.save_intermediate if self.profile.logging else False  # 从配置获取
            
            if should_save_intermediate:  # 如果需要保存中间结果
                self._save_intermediate_results(  # 保存中间结果
                    Path(output_path), perception_result, adjustment_plan, restoration_result
                )
        
        return results  # 返回结果
    
    def _save_output_image(self, output_path: Union[str, Path], image: Image.Image, results: Dict[str, Any]):
        """Helper to save output image with proper format handling 保存输出图像的辅助函数，处理格式"""
        output_path = Path(output_path)  # 转换为Path对象
        output_path.parent.mkdir(parents=True, exist_ok=True)  # 创建输出目录
        
        # Get output format from profile or file extension
        # 从配置或文件扩展名获取输出格式
        output_format = self.profile.logging.output_format if self.profile.logging else "png"  # 获取输出格式
        if output_path.suffix:  # 如果有文件扩展名
            output_format = output_path.suffix[1:].lower()  # 使用文件扩展名作为格式
        
        image.save(output_path, format=output_format)  # 保存图像
        results["output_path"] = str(output_path)  # 将输出路径添加到结果中
        logger.info(f"Saved result to: {output_path}")  # 记录保存位置
    
    def _clean_filename(self, text: str, max_length: int = 50) -> str:
        """清理文本以用作文件名 Clean text for use as filename"""
        import re  # 导入正则表达式模块

        # 移除或替换不安全的字符
        # Remove or replace unsafe characters
        text = re.sub(r'[<>:"/\\|?*]', '_', text)  # 替换文件名中的不安全字符
        # 移除多余的空格和特殊字符
        # Remove extra spaces and special characters
        text = re.sub(r'[^\w\s-]', '', text)  # 移除非字母数字和空格的字符
        # 替换空格为下划线
        # Replace spaces with underscores
        text = re.sub(r'\s+', '_', text)  # 将空格替换为下划线
        # 截断到最大长度
        # Truncate to maximum length
        if len(text) > max_length:  # 如果文本太长
            text = text[:max_length].rstrip('_')  # 截断并移除末尾的下划线

        return text or "step"  # 如果清理后为空，使用默认名称
    
    def _save_intermediate_results(self, output_path: Path, perception_result,
                                  adjustment_plan, restoration_result):
        """Save intermediate processing results 保存中间处理结果"""
        base_path = output_path.parent / output_path.stem  # 构建基础路径

        # Save original image
        # 保存原始图像
        restoration_result.original_image.save(f"{base_path}_original.png")  # 保存原始图像
        
        # Save intermediate execution results
        # 保存中间执行结果
        successful_steps = []  # 成功步骤列表
        for i, exec_result in enumerate(restoration_result.operation_results):  # 遍历应用的任务
            if exec_result.success:  # 如果任务成功
                # 清理和截断指令文本以避免文件名过长
                # Clean and truncate instruction text to avoid overly long filenames
                instruction_clean = self._clean_filename(exec_result.instruction)  # 清理指令文本
                step_path = f"{base_path}_step_{i+1}_{instruction_clean}.png"  # 构建步骤文件路径
                exec_result.output_image.save(step_path)  # 保存步骤结果图像
                successful_steps.append((i, exec_result))  # 添加到成功步骤列表
        
        # Save comparison grid if we have intermediate steps
        # 如果有中间步骤则保存比较网格
        if successful_steps:  # 如果有成功的步骤
            images = [restoration_result.original_image, restoration_result.final_image]  # 图像列表
            labels = ["Original", "Final"]  # 标签列表
            
            # Add successful intermediate results
            # 添加成功的中间结果
            for i, exec_result in successful_steps:  # 遍历成功步骤
                images.append(exec_result.output_image)  # 添加输出图像
                labels.append(f"Step {i+1}")  # 添加步骤标签
            
            if len(images) > 2:  # 如果有多于2张图像
                grid_image = ImageOperations.create_comparison_grid(images)  # 创建比较网格
                grid_image.save(f"{base_path}_comparison.png")  # 保存比较网格
        
        logger.info(f"Saved intermediate results to {base_path}_*")  # 记录保存的中间结果
    
    def batch_process(self, input_dir: Union[str, Path], 
                     output_dir: Union[str, Path],
                     pattern: str = "*.png") -> List[Dict[str, Any]]:
        """
        Process multiple images in batch
        批量处理多张图像
        
        Args:
            input_dir: Directory containing input images 包含输入图像的目录
            output_dir: Directory to save output images 保存输出图像的目录
            pattern: File pattern to match 要匹配的文件模式
            
        Returns:
            List of processing results 处理结果列表
        """
        input_dir = Path(input_dir)  # 转换输入目录为Path对象
        output_dir = Path(output_dir)  # 转换输出目录为Path对象
        output_dir.mkdir(parents=True, exist_ok=True)  # 创建输出目录
        
        # Find input images
        # 查找输入图像
        image_files = list(input_dir.glob(pattern))  # 根据模式查找图像文件
        logger.info(f"Found {len(image_files)} images to process")  # 记录找到的图像数量
        
        results = []  # 结果列表
        
        for i, image_file in enumerate(image_files):  # 遍历图像文件
            logger.info(f"Processing {i+1}/{len(image_files)}: {image_file.name}")  # 记录处理进度
            
            try:
                output_file = output_dir / f"enhanced_{image_file.name}"  # 构建输出文件路径
                result = self.process_image(image_file, output_file)  # 处理图像
                result["success"] = True  # 标记成功
                results.append(result)  # 添加到结果列表
                
            except Exception as e:  # 捕获异常
                logger.error(f"Failed to process {image_file}: {e}")  # 记录错误
                results.append({  # 添加错误结果
                    "input_path": str(image_file),  # 输入路径
                    "error": str(e),  # 错误信息
                    "success": False  # 标记失败
                })
        
        # Generate summary
        # 生成摘要
        successful = sum(1 for r in results if r.get("success", False))  # 计算成功数量
        logger.info(f"Batch processing complete: {successful}/{len(results)} successful")  # 记录批处理完成情况
        
        return results  # 返回结果列表
    
    def analyze_only(self, input_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze image without making adjustments
        只分析图像而不进行调整
        
        Args:
            input_path: Path to input image 输入图像路径
            
        Returns:
            Analysis results 分析结果
        """
        image = Image.open(input_path).convert("RGB")  # 打开并转换图像为RGB格式
        
        # Run perception analysis
        # 运行感知分析
        perception_result = self.perception_agent.analyze_image(image)  # 分析图像
        
        # Generate plan (but don't execute)
        # 生成计划（但不执行）
        adjustment_plan = self.planning_agent.generate_plan(perception_result, image)  # 生成调整计划
        
        return {  # 返回分析结果
            "input_path": str(input_path),  # 输入路径
            "content_description": perception_result.content_description,  # 内容描述
            "composition_issues": perception_result.composition_issues,  # 构图问题
            "issue_count": len(perception_result.composition_issues),  # 问题数量
            "recommendations": perception_result.recommendations,  # 建议
            "suggested_plan": {  # 建议的计划
                "num_instructions": len(adjustment_plan.instructions),  # 指令数量
                "instructions": [inst.instruction for inst in adjustment_plan.instructions]  # 指令列表
            }
        }
    
    def compare_images(self, image1_path: Union[str, Path], 
                      image2_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Compare two images
        比较两张图像
        
        Args:
            image1_path: Path to first image 第一张图像路径
            image2_path: Path to second image 第二张图像路径
            
        Returns:
            Comparison results 比较结果
        """
        return self.perception_agent.compare_images(image1_path, image2_path)  # 调用感知代理比较图像
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system configuration and status
        获取系统配置和状态
        
        Returns:
            System information dictionary 系统信息字典
        """
        return {  # 返回系统信息
            "profile": {  # 配置信息
                "perception_model": self.profile.profile.perception_model if self.profile.profile else None,  # 感知模型
                "aesthetic_models": self.profile.profile.aesthetic_models if self.profile.profile else None,  # 美学模型
                "enabled_tasks": self.profile.profile.tasks if self.profile.profile else None,  # 启用的任务
                "device": self.profile.profile.device if self.profile.profile else None  # 设备
            },
            "agents": {  # 代理状态
                "perception_agent": "initialized",  # 感知代理已初始化
                "planning_agent": "initialized",  # 规划代理已初始化
                "restoration_agent": "initialized"  # 修复代理已初始化
            },
            "config_path": str(self.profile.config_path) if self.profile.config_path else None  # 配置文件路径
        }
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        Update system configuration
        更新系统配置
        
        Args:
            updates: Configuration updates 配置更新
        """
        for section, section_updates in updates.items():  # 遍历更新项
            self.profile.update_config(section, section_updates)  # 更新配置
        
        # Reinitialize agents if needed
        # 如果需要则重新初始化代理
        if "profile" in updates:  # 如果更新了profile配置
            self.perception_agent = PerceptionAgent(self.profile)  # 重新初始化感知代理
            self.planning_agent = PlanningAgent(self.profile)  # 重新初始化规划代理
            self.restoration_agent = RestorationAgent(self.profile)  # 重新初始化修复代理
            
        logger.info("Configuration updated and agents reinitialized")  # 记录配置更新和代理重新初始化


def main():
    """Main entry point for command line usage 命令行使用的主入口点"""
    import sys  # 导入系统模块
    from .cli import cli  # 导入命令行接口

    # If called as module, use CLI
    # 如果作为模块调用，使用CLI
    if len(sys.argv) > 1:  # 如果有命令行参数
        cli()  # 调用CLI
    else:
        # Show help if no arguments
        # 如果没有参数则显示帮助
        cli(['--help'])  # 显示帮助信息


if __name__ == "__main__":  # 如果作为主程序运行
    main()  # 调用主函数
