"""
Unified Data Types and Structures for Agents

简化的数据类型定义，删除了未使用的复杂组件。
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from PIL import Image


# ============================================================================
# 基础枚举定义
# ============================================================================

# OperationStatus 枚举已删除，因为没有被使用


class Priority(Enum):
    """优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


# QualityLevel 枚举已删除，因为没有被使用


# ============================================================================
# 基础数据结构
# ============================================================================

# BaseEntity 类已删除，因为它是空的且没有被使用




@dataclass
class ProcessingResult:
    """简化的处理结果基类"""
    success: bool = True
    message: str = ""
    processing_time: float = 0.0


# ============================================================================
# 图像相关数据结构
# ============================================================================

@dataclass
class ImageInfo:
    """简化的图像信息"""
    width: int
    height: int
    mode: str  # RGB, RGBA, L, etc.
    format: Optional[str] = None  # JPEG, PNG, etc.
    
    @classmethod
    def from_image(cls, image: Image.Image) -> 'ImageInfo':
        """从 PIL Image 创建"""
        return cls(
            width=image.width,
            height=image.height,
            mode=image.mode,
            format=getattr(image, 'format', None)
        )


@dataclass
class ImageScore:
    """极简的图像评分"""
    aesthetic_score: float
    technical_score: float = 0.0
    composition_score: float = 0.0
    overall_score: float = 0.0


# ============================================================================
# 感知相关数据结构
# ============================================================================

@dataclass
class PerceptionResult(ProcessingResult):
    """统一的感知结果"""
    content_description: str = ""
    composition_issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    image_info: Optional[ImageInfo] = None
    
    @property
    def issue_count(self) -> int:
        """问题数量"""
        return len(self.composition_issues)
    
    @property
    def has_issues(self) -> bool:
        """是否有问题"""
        return self.issue_count > 0


# ============================================================================
# 规划相关数据结构
# ============================================================================

@dataclass
class EditingInstruction:
    """简化的编辑指令"""
    instruction: str = ""
    priority: Priority = Priority.NORMAL
    step_id: Optional[str] = None


@dataclass
class AdjustmentPlan:
    """简化的调整计划"""
    instructions: List[EditingInstruction] = field(default_factory=list)


# ============================================================================
# 执行相关数据结构
# ============================================================================

@dataclass
class OperationResult(ProcessingResult):
    """简化的操作结果"""
    input_image: Optional[Image.Image] = None
    output_image: Optional[Image.Image] = None
    score_before: float = 0.0
    score_after: float = 0.0
    improvement: float = 0.0
    operation_type: str = ""
    model_used: Optional[str] = None
    instruction: str = ""


@dataclass
class BatchResult(ProcessingResult):
    """简化的批处理结果"""
    original_image: Optional[Image.Image] = None
    final_image: Optional[Image.Image] = None
    operation_results: List[OperationResult] = field(default_factory=list)
    total_improvement: float = 0.0
    final_score: float = 0.0
    original_score: Optional[float] = None

    @property
    def improvement(self) -> float:
        """向后兼容属性：总改进值"""
        return self.total_improvement


# ============================================================================
# 反馈相关数据结构
# ============================================================================

@dataclass
class FeedbackMetrics:
    """简化的反馈指标 - 删除了复杂的计算逻辑"""
    aesthetic_score_before: float = 0.0
    aesthetic_score_after: float = 0.0
    aesthetic_improvement: float = 0.0
    overall_improvement: float = 0.0
    processing_time: float = 0.0


@dataclass
class FeedbackResult(ProcessingResult):
    """反馈结果"""
    feedback_type: str = "info"  # success, warning, failure, critical
    metrics: Optional[FeedbackMetrics] = None
    recommendations: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)


# ============================================================================
# 控制相关数据结构
# ============================================================================

@dataclass
class ControllerDecision:
    """简化的控制器决策"""
    strategy: str = "continue"  # continue, rollback_local, rollback_global, abort
    reason: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
