"""
Profile Configuration Module - 配置文件管理模块

Manages system configuration, model selection, and parameter settings
for the AutoComposeAgent system.
管理 AutoComposeAgent 系统的系统配置、模型选择和参数设置。
"""

import yaml  # 导入 YAML 解析库，用于读写配置文件
from typing import Dict, List, Any, Optional, Union  # 导入类型注解相关的类型
from pathlib import Path  # 导入路径处理库，用于跨平台路径操作
from pydantic import BaseModel, Field, validator  # 导入 Pydantic 数据验证库
import logging  # 导入日志记录库

logger = logging.getLogger(__name__)  # 创建当前模块的日志记录器


# 未使用的配置类已删除：CropConfig, RotateConfig, RecenterConfig, ColorAdjustConfig


class ProfileConfig(BaseModel):
    """Main profile configuration - 主要配置文件类"""
    # 感知模型名称，用于图像理解和分析
    perception_model: str = Field(default="llama-vision")
    # 规划模型名称，用于 LLM 思维规划
    planning_model: str = Field(default="qwen2.5")  # Added for LLM thinking planning
    # 美学评估模型列表，用于评估图像美学质量
    aesthetic_models: List[str] = Field(default=["shadow-aesthetic", "laion-aesthetic"])
    # 恢复偏好设置，决定优先恢复的方面
    restore_preference: str = Field(default="composition")
    # 回滚阈值，当质量下降超过此值时触发回滚
    rollback_threshold: float = Field(default=0.5, ge=0, le=1)
    # 任务列表，定义要执行的图像处理任务
    tasks: List[str] = Field(default=["crop", "rotate", "recenter"])
    # 最大迭代次数，防止无限循环
    max_iterations: int = Field(default=5, ge=1, le=20)
    # 计算设备，cuda 或 cpu
    device: str = Field(default="cuda")
    # 批处理大小
    batch_size: int = Field(default=1, ge=1)
    # 随机种子，用于可重现的评估结果
    random_seed: Optional[int] = Field(default=42, description="Random seed for reproducible evaluation")

    # Enhanced features flags - 增强功能开关
    # 启用思维规划功能
    enable_thinking_planning: bool = Field(default=False)
    # 启用多模态大语言模型恢复功能
    enable_mllm_restoration: bool = Field(default=False)
    # 启用反馈循环功能
    enable_feedback_loop: bool = Field(default=False)
    # 启用规划链功能
    enable_planning_chain: bool = Field(default=False)

    # Planning Chain settings - 规划链设置
    # 最大重新规划尝试次数
    max_replanning_attempts: int = Field(default=3, ge=1, le=10)
    # 最大局部回滚次数
    max_local_rollbacks: int = Field(default=2, ge=0, le=5)
    # 最大全局回滚次数
    max_global_rollbacks: int = Field(default=1, ge=0, le=3)
    # 最小改进阈值，低于此值认为没有改进
    min_improvement_threshold: float = Field(default=-0.1, ge=-1.0, le=0.0)

    # Feedback settings - 反馈设置
    # 成功阈值，超过此值认为操作成功
    feedback_success_threshold: float = Field(default=0.05, ge=0.0, le=1.0)
    # 警告阈值，介于成功和失败之间
    feedback_warning_threshold: float = Field(default=-0.02, ge=-1.0, le=0.0)
    # 失败阈值，低于此值认为操作失败
    feedback_failure_threshold: float = Field(default=-0.1, ge=-1.0, le=0.0)
    
    # 操作配置对象已删除，因为没有被使用
    
    @validator('perception_model')  # 感知模型验证器
    def validate_perception_model(cls, v):
        # 定义有效的感知模型列表
        valid_models = ["llama-vision", "gpt-4-vision", "claude-vision"]
        if v not in valid_models:  # 检查模型是否在有效列表中
            raise ValueError(f"perception_model must be one of {valid_models}")
        return v

    @validator('planning_model')  # 规划模型验证器
    def validate_planning_model(cls, v):
        # 定义有效的规划模型列表
        valid_models = ["gpt-4", "claude-3", "qwen2.5"]
        if v not in valid_models:  # 检查模型是否在有效列表中
            raise ValueError(f"planning_model must be one of {valid_models}")
        return v
    
    @validator('restore_preference')  # 恢复偏好验证器
    def validate_restore_preference(cls, v):
        # 定义有效的恢复偏好选项
        valid_prefs = ["composition", "aesthetic", "balanced"]
        if v not in valid_prefs:  # 检查偏好是否在有效列表中
            raise ValueError(f"restore_preference must be one of {valid_prefs}")
        return v
    
    @validator('tasks')  # 任务列表验证器
    def validate_tasks(cls, v):
        # 定义有效的任务类型
        valid_tasks = ["crop", "rotate", "recenter", "color_adjust", "brightness"]
        for task in v:  # 遍历检查每个任务
            if task not in valid_tasks:  # 检查任务是否在有效列表中
                raise ValueError(f"Task '{task}' not in valid tasks: {valid_tasks}")
        return v


class APIConfig(BaseModel):
    """API configuration - API 配置类"""
    # OpenAI API 密钥，用于 GPT 模型
    openai_api_key: Optional[str] = None
    # Anthropic API 密钥，用于 Claude 模型
    anthropic_api_key: Optional[str] = None

    # Qwen2.5 configuration - Qwen2.5 配置
    # Qwen API 密钥
    qwen_api_key: Optional[str] = None
    # Qwen 模型名称
    qwen_model: str = Field(default="qwen2.5-72b-instruct")
    # Qwen API 基础 URL
    qwen_base_url: Optional[str] = None
    # 最大生成令牌数
    qwen_max_tokens: int = Field(default=4000, ge=1, le=8192)
    # 生成温度，控制随机性
    qwen_temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    # Top-p 采样参数
    qwen_top_p: float = Field(default=0.8, ge=0.0, le=1.0)


# QwenSpecificConfig 已删除，因为没有被使用


# ThinkingConfig 已删除，因为没有被使用


class LoggingConfig(BaseModel):
    """Logging configuration - 日志配置类"""
    # 日志级别
    level: str = Field(default="INFO")
    # 保存中间结果
    save_intermediate: bool = Field(default=True)
    # 输出格式
    output_format: str = Field(default="jpg")
    
    @validator('level')  # 日志级别验证器
    def validate_level(cls, v):
        # 定义有效的日志级别
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
        if v not in valid_levels:  # 检查级别是否有效
            raise ValueError(f"level must be one of {valid_levels}")
        return v
    
    @validator('output_format')  # 输出格式验证器
    def validate_output_format(cls, v):
        # 定义有效的输出格式
        valid_formats = ["jpg", "png", "webp"]
        if v not in valid_formats:  # 检查格式是否有效
            raise ValueError(f"output_format must be one of {valid_formats}")
        return v


# PerformanceConfig 已删除，因为没有被使用


class Profile:
    """
    Main Profile class for managing system configuration
    主要的配置管理类，用于管理系统配置
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize Profile with configuration
        使用配置初始化 Profile 对象
        
        Args:
            config_path: Path to YAML configuration file - YAML 配置文件路径
        """
        # 如果提供了路径则转换为 Path 对象，否则为 None
        self.config_path = Path(config_path) if config_path else None
        # 初始化各配置对象为 None
        self.profile: Optional[ProfileConfig] = None
        self.api: Optional[APIConfig] = None
        self.logging: Optional[LoggingConfig] = None

        # 如果配置文件存在则加载，否则使用默认配置
        if self.config_path and self.config_path.exists():
            self.load_config()  # 加载配置文件
        else:
            self._load_defaults()  # 加载默认配置
    
    def load_config(self, config_path: Optional[Union[str, Path]] = None) -> None:
        """
        Load configuration from YAML file
        从 YAML 文件加载配置

        Args:
            config_path: Path to configuration file - 配置文件路径
        """
        if config_path:  # 如果提供了新路径
            self.config_path = Path(config_path)  # 更新配置文件路径

        # 检查配置文件是否存在
        if not self.config_path or not self.config_path.exists():
            logger.warning(f"Config file not found: {self.config_path}. Using defaults.")
            self._load_defaults()  # 文件不存在时使用默认配置
            return

        try:
            # 打开并解析 YAML 文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)  # 安全加载 YAML 数据

            # 检查配置数据是否为字典格式
            if not isinstance(config_data, dict):
                raise ValueError("Configuration file must contain a dictionary")

            # Validate and load each section - 验证并加载每个配置部分
            try:
                # 从配置数据中获取 profile 部分，如果不存在则使用空字典
                self.profile = ProfileConfig(**config_data.get('profile', {}))
            except Exception as e:
                logger.error(f"Invalid profile configuration: {e}")
                self.profile = ProfileConfig()  # 使用默认配置

            try:
                # 加载 API 配置
                self.api = APIConfig(**config_data.get('api', {}))
            except Exception as e:
                logger.error(f"Invalid API configuration: {e}")
                self.api = APIConfig()

            try:
                # 加载日志配置
                self.logging = LoggingConfig(**config_data.get('logging', {}))
            except Exception as e:
                logger.error(f"Invalid logging configuration: {e}")
                self.logging = LoggingConfig()

            # Validate the complete configuration - 验证完整配置
            if self.validate_config():
                logger.info(f"Configuration loaded and validated from {self.config_path}")
            else:
                logger.warning("Configuration loaded but validation failed")

        except yaml.YAMLError as e:  # YAML 解析错误
            logger.error(f"YAML parsing error: {e}")
            self._load_defaults()
        except Exception as e:  # 其他错误
            logger.error(f"Error loading config: {e}")
            self._load_defaults()
    
    def _load_defaults(self) -> None:
        """Load default configuration - 加载默认配置"""
        # 使用默认参数初始化所有配置对象
        self.profile = ProfileConfig()
        self.api = APIConfig()
        self.logging = LoggingConfig()
        logger.info("Using default configuration")
    
    def save_config(self, config_path: Optional[Union[str, Path]] = None) -> None:
        """
        Save current configuration to YAML file
        将当前配置保存到 YAML 文件
        
        Args:
            config_path: Path to save configuration file - 保存配置文件的路径
        """
        if config_path:  # 如果提供了新路径
            self.config_path = Path(config_path)
        
        if not self.config_path:  # 如果没有指定路径
            raise ValueError("No config path specified")
        
        # 构建配置数据字典
        config_data = {
            'profile': self.profile.dict() if self.profile else {},
            'api': self.api.dict() if self.api else {},
            'logging': self.logging.dict() if self.logging else {}
        }
        
        # 确保父目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入 YAML 文件
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
        
        logger.info(f"Configuration saved to {self.config_path}")
    
    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """
        Get configuration for specific model type
        获取特定模型类型的配置
        
        Args:
            model_type: Type of model (perception, aesthetic, etc.) - 模型类型
            
        Returns:
            Model configuration dictionary - 模型配置字典
        """
        if not self.profile:  # 如果没有配置
            return {}
        
        if model_type == "perception":  # 感知模型配置
            return {
                "model_name": self.profile.perception_model,
                "device": self.profile.device,
                "batch_size": self.profile.batch_size
            }
        elif model_type == "aesthetic":  # 美学模型配置
            return {
                "models": self.profile.aesthetic_models,
                "device": self.profile.device,
                "threshold": self.profile.rollback_threshold
            }
        else:
            return {}  # 未知模型类型返回空字典
    
    # get_task_config 方法已删除，因为相关配置类不存在
    
    def update_config(self, section: str, updates: Dict[str, Any]) -> None:
        """
        Update configuration section
        更新配置部分
        
        Args:
            section: Configuration section to update - 要更新的配置部分
            updates: Dictionary of updates to apply - 要应用的更新字典
        """
        # 根据部分名称更新相应的配置
        if section == "profile" and self.profile:
            for key, value in updates.items():  # 遍历更新项
                if hasattr(self.profile, key):  # 检查属性是否存在
                    setattr(self.profile, key, value)  # 设置属性值
        elif section == "api" and self.api:
            for key, value in updates.items():
                if hasattr(self.api, key):
                    setattr(self.api, key, value)
        elif section == "logging" and self.logging:
            for key, value in updates.items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
        elif section == "performance" and self.performance:
            for key, value in updates.items():
                if hasattr(self.performance, key):
                    setattr(self.performance, key, value)
        
        logger.info(f"Updated {section} configuration")
    
    def validate_config(self) -> bool:
        """
        Validate current configuration
        验证当前配置

        Returns:
            True if configuration is valid - 如果配置有效则返回 True
        """
        is_valid = True  # 配置有效性标志

        try:
            # Validate profile configuration - 验证配置文件配置
            if self.profile:
                ProfileConfig(**self.profile.dict())  # 重新验证配置

                # Additional validation checks - 额外的验证检查
                # 检查 GPT-4 Vision 是否有对应的 API 密钥
                if self.profile.perception_model == "gpt-4-vision" and not (self.api and self.api.openai_api_key):
                    logger.warning("GPT-4 Vision selected but no OpenAI API key provided")
                    is_valid = False

                # 检查 Claude Vision 是否有对应的 API 密钥
                if self.profile.perception_model == "claude-vision" and not (self.api and self.api.anthropic_api_key):
                    logger.warning("Claude Vision selected but no Anthropic API key provided")
                    is_valid = False

                # Check if aesthetic models are valid (imscore-based models)
                # 检查美学模型是否有效（基于 imscore 的模型）
                valid_aesthetic_models = [
                    "shadow-aesthetic", "laion-aesthetic", "clip-aesthetic",
                    "siglip-aesthetic", "dinov2-aesthetic", "clipscore",
                    "pickscore", "hps", "mps", "imagereward"
                ]
                for model in self.profile.aesthetic_models:  # 遍历美学模型
                    if model not in valid_aesthetic_models:  # 检查模型是否有效
                        logger.warning(f"Unknown aesthetic model: {model}")
                        is_valid = False

                # Check device availability - 检查设备可用性
                if self.profile.device == "cuda":
                    try:
                        import torch
                        if not torch.cuda.is_available():  # 检查 CUDA 是否可用
                            logger.warning("CUDA device selected but not available, will fall back to CPU")
                    except ImportError:
                        logger.warning("PyTorch not available, cannot check CUDA availability")

            # Validate API configuration - 验证 API 配置
            if self.api:
                APIConfig(**self.api.dict())

            # Validate logging configuration - 验证日志配置
            if self.logging:
                LoggingConfig(**self.logging.dict())

            if is_valid:  # 如果配置有效
                logger.debug("Configuration validation passed")

            return is_valid

        except Exception as e:  # 捕获验证过程中的异常
            logger.error(f"Configuration validation failed: {e}")
            return False

    def health_check(self) -> Dict[str, Any]:
        """
        Perform a comprehensive health check of the configuration
        执行配置的综合健康检查

        Returns:
            Dictionary with health check results - 包含健康检查结果的字典
        """
        # 初始化健康状态字典
        health_status = {
            "overall_status": "healthy",  # 总体状态
            "issues": [],  # 问题列表
            "warnings": [],  # 警告列表
            "recommendations": []  # 建议列表
        }

        try:
            # Check if configuration is loaded - 检查配置是否已加载
            if not self.profile:
                health_status["issues"].append("No profile configuration loaded")
                health_status["overall_status"] = "unhealthy"
                return health_status

            # Check perception model setup - 检查感知模型设置
            if self.profile.perception_model == "gpt-4-vision":
                if not (self.api and self.api.openai_api_key):
                    health_status["issues"].append("OpenAI API key required for GPT-4 Vision")
                    health_status["overall_status"] = "unhealthy"
            elif self.profile.perception_model == "claude-vision":
                if not (self.api and self.api.anthropic_api_key):
                    health_status["issues"].append("Anthropic API key required for Claude Vision")
                    health_status["overall_status"] = "unhealthy"
            elif self.profile.perception_model == "llama-vision":
                try:
                    import transformers  # 检查 transformers 库是否可用
                    health_status["recommendations"].append("Llama Vision requires transformers library")
                except ImportError:
                    health_status["issues"].append("transformers library required for Llama Vision")
                    health_status["overall_status"] = "unhealthy"

            # Check aesthetic models (imscore-based) - 检查美学模型（基于 imscore）
            try:
                import imscore  # 检查 imscore 库是否可用
                health_status["recommendations"].append("imscore library available for aesthetic models")
            except ImportError:
                health_status["issues"].append("imscore library required for aesthetic models")
                health_status["overall_status"] = "unhealthy"

            # Check device availability - 检查设备可用性
            if self.profile.device == "cuda":
                try:
                    import torch
                    if not torch.cuda.is_available():  # 检查 CUDA 是否可用
                        health_status["warnings"].append("CUDA not available, will use CPU")
                except ImportError:
                    health_status["warnings"].append("PyTorch not installed")

            # Check task configuration - 检查任务配置
            if not self.profile.tasks:
                health_status["warnings"].append("No tasks enabled")

            # Set overall status based on issues - 根据问题设置总体状态
            if health_status["issues"]:
                health_status["overall_status"] = "unhealthy"
            elif health_status["warnings"]:
                health_status["overall_status"] = "warning"

        except Exception as e:  # 捕获健康检查过程中的异常
            health_status["issues"].append(f"Health check failed: {e}")
            health_status["overall_status"] = "error"

        return health_status  # 返回健康状态字典
