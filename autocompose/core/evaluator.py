"""
Aesthetic Evaluator Module

Implements aesthetic evaluation models using the imscore library for image quality assessment.
"""

import torch
import torch.nn as nn
import numpy as np
import random
import os
from typing import Dict, List, Union, Optional, Tuple
from PIL import Image
import logging
from abc import ABC, abstractmethod
from einops import rearrange

logger = logging.getLogger(__name__)


def set_random_seed(seed: int = 42) -> None:
    """
    Set random seed for reproducible results across all libraries

    Args:
        seed: Random seed value
    """
    # Set environment variables for deterministic behavior
    os.environ["PYTHONHASHSEED"] = str(seed)
    os.environ["CUBLAS_WORKSPACE_CONFIG"] = ":4096:8"

    # Set seeds for all random number generators
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

    # Configure PyTorch for deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

    # Use deterministic algorithms when available, but allow fallback for unsupported operations
    try:
        torch.use_deterministic_algorithms(True, warn_only=True)
    except TypeError:
        # Older PyTorch versions don't support warn_only parameter
        try:
            torch.use_deterministic_algorithms(True)
        except Exception:
            # Some PyTorch versions don't support this at all
            pass
    except Exception:
        # Some PyTorch versions don't support this
        pass

    logger.debug(f"Random seed set to {seed} for reproducible evaluation")


class BaseAestheticModel(ABC):
    """Base class for aesthetic evaluation models"""
    
    def __init__(self, device: str = "cuda"):
        self.device = device
        self.model = None
        
    @abstractmethod
    def load_model(self) -> None:
        """Load the aesthetic model"""
        pass
    
    @abstractmethod
    def evaluate(self, image: Union[Image.Image, torch.Tensor]) -> float:
        """
        Evaluate aesthetic score of an image
        
        Args:
            image: Input image
            
        Returns:
            Aesthetic score (0-1)
        """
        pass


class ImscoreAestheticModel(BaseAestheticModel):
    """Imscore-based aesthetic evaluation model"""

    def __init__(self, model_name: str, device: str = "cuda", random_seed: Optional[int] = 42):
        """
        Initialize imscore aesthetic model

        Args:
            model_name: Name of the imscore model to use
            device: Device to run model on
            random_seed: Random seed for reproducible results (None to disable)
        """
        super().__init__(device)
        self.model_name = model_name
        self.model = None
        self.random_seed = random_seed
        self.load_model()

    def load_model(self) -> None:
        """Load imscore aesthetic model"""
        try:
            # Import imscore modules based on model type
            if "shadow" in self.model_name.lower():
                from imscore.aesthetic.model import ShadowAesthetic
                self.model = ShadowAesthetic.from_pretrained("RE-N-Y/aesthetic-shadow-v2")
                logger.info(f"Loaded ShadowAesthetic model: {self.model_name}")

            elif "laion" in self.model_name.lower():
                from imscore.aesthetic.model import LAIONAestheticScorer
                self.model = LAIONAestheticScorer.from_pretrained("RE-N-Y/laion-aesthetic")
                logger.info(f"Loaded LAION Aesthetic model: {self.model_name}")

            elif "clip" in self.model_name.lower() and "aesthetic" in self.model_name.lower():
                from imscore.aesthetic.model import CLIPAestheticScorer
                self.model = CLIPAestheticScorer.from_pretrained("RE-N-Y/imreward-overall_rating-clip")
                logger.info(f"Loaded CLIP Aesthetic model: {self.model_name}")

            elif "siglip" in self.model_name.lower() and "aesthetic" in self.model_name.lower():
                from imscore.aesthetic.model import SiglipAestheticScorer
                self.model = SiglipAestheticScorer.from_pretrained("RE-N-Y/imreward-overall_rating-siglip")
                logger.info(f"Loaded Siglip Aesthetic model: {self.model_name}")

            elif "dinov2" in self.model_name.lower() and "aesthetic" in self.model_name.lower():
                from imscore.aesthetic.model import Dinov2AestheticScorer
                self.model = Dinov2AestheticScorer.from_pretrained("RE-N-Y/imreward-overall_rating-dinov2")
                logger.info(f"Loaded DINOv2 Aesthetic model: {self.model_name}")

            elif "clipscore" in self.model_name.lower():
                from imscore.preference.model import CLIPScore
                # CLIPScore needs a tag parameter, using a generic one
                self.model = CLIPScore.from_pretrained("RE-N-Y/clipscore-vit-large-patch14")
                logger.info(f"Loaded CLIPScore model: {self.model_name}")

            elif "pickscore" in self.model_name.lower():
                from imscore.pickscore.model import PickScorer
                self.model = PickScorer.from_pretrained("RE-N-Y/pickscore")
                logger.info(f"Loaded PickScore model: {self.model_name}")

            elif "hps" in self.model_name.lower():
                from imscore.hps.model import HPSv2
                self.model = HPSv2.from_pretrained("RE-N-Y/hpsv21")
                logger.info(f"Loaded HPSv2 model: {self.model_name}")

            elif "mps" in self.model_name.lower():
                from imscore.mps.model import MPS
                self.model = MPS.from_pretrained("RE-N-Y/mpsv1")
                logger.info(f"Loaded MPS model: {self.model_name}")

            elif "imagereward" in self.model_name.lower():
                from imscore.imreward.model import ImageReward
                self.model = ImageReward.from_pretrained("RE-N-Y/ImageReward")
                logger.info(f"Loaded ImageReward model: {self.model_name}")

            else:
                logger.error(f"Unknown imscore model: {self.model_name}")
                self.model = None

        except Exception as e:
            logger.error(f"Failed to load imscore model {self.model_name}: {e}")
            self.model = None

    def evaluate(self, image: Union[Image.Image, torch.Tensor]) -> float:
        """
        Evaluate aesthetic score using imscore model

        Args:
            image: Input image (PIL Image or torch.Tensor)

        Returns:
            Aesthetic score (0-1)
        """
        if self.model is None:
            logger.warning(f"Model {self.model_name} not loaded, returning default score")
            return 0.5

        # Set random seed for reproducible results
        if self.random_seed is not None:
            set_random_seed(self.random_seed)

        try:
            # Convert image to the format expected by imscore
            if isinstance(image, Image.Image):
                # Convert PIL to tensor in range [0, 1]
                pixels = np.array(image)
                pixels = rearrange(torch.tensor(pixels), "h w c -> 1 c h w") / 255.0
            elif isinstance(image, torch.Tensor):
                # Ensure tensor is in correct format
                if image.dim() == 3:
                    pixels = image.unsqueeze(0)  # Add batch dimension
                else:
                    pixels = image

                # Ensure range is [0, 1]
                if pixels.max() > 1.0:
                    pixels = pixels / 255.0
            else:
                logger.error(f"Unsupported image type: {type(image)}")
                return 0.5

            # Get score from imscore model
            # For models that need prompts, we'll use a generic prompt
            if hasattr(self.model, 'score') and 'prompt' in str(self.model.score.__code__.co_varnames):
                # This model needs prompts (preference models)
                prompts = "a high quality image"
                score = self.model.score(pixels, prompts)
            else:
                # This model only needs pixels (aesthetic models)
                score = self.model.score(pixels)

            # Convert score to float and ensure it's in [0, 1] range
            if isinstance(score, torch.Tensor):
                score = float(score.cpu().item())
            else:
                score = float(score)

            # Normalize score to [0, 1] if needed
            # Some models might return scores in different ranges
            if score < 0:
                score = 0.0
            elif score > 1:
                score = min(score / 10.0, 1.0)  # Some models return scores up to 10

            return score

        except Exception as e:
            logger.error(f"Error evaluating with {self.model_name}: {e}")
            return 0.5


class AestheticEvaluator:
    """
    Main aesthetic evaluator that manages multiple models
    """

    # Class-level model cache to avoid reloading models
    _model_cache = {}

    def __init__(self, models: List[str], device: str = "cuda", random_seed: Optional[int] = 42):
        """
        Initialize aesthetic evaluator

        Args:
            models: List of model names to use
            device: Device to run models on
            random_seed: Random seed for reproducible results (None to disable)
        """
        self.device = device
        self.models = {}
        self.model_weights = {}
        self.random_seed = random_seed

        # Initialize requested models
        for model_name in models:
            self._load_model(model_name)
    
    def _load_model(self, model_name: str) -> None:
        """Load a specific aesthetic model with caching using imscore"""
        model_name_lower = model_name.lower()
        cache_key = f"{model_name_lower}_{self.device}"

        # Check if model is already cached
        if cache_key in self._model_cache:
            logger.info(f"Using cached {model_name} model")
            self.models[model_name_lower] = self._model_cache[cache_key]
            self.model_weights[model_name_lower] = 1.0
            return

        # Load new model using imscore
        try:
            model = ImscoreAestheticModel(model_name, self.device, self.random_seed)
            if model.model is not None:
                self.models[model_name_lower] = model
                self.model_weights[model_name_lower] = 1.0
                self._model_cache[cache_key] = model
                logger.info(f"Imscore model {model_name} loaded and cached")
            else:
                logger.error(f"Failed to load imscore model: {model_name}")

        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
    
    def evaluate(self, image: Union[Image.Image, torch.Tensor], 
                 model_weights: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """
        Evaluate image using all loaded models
        
        Args:
            image: Input image
            model_weights: Optional custom weights for models
            
        Returns:
            Dictionary of scores from each model plus combined score
        """
        scores = {}
        
        # Get scores from each model
        for model_name, model in self.models.items():
            try:
                score = model.evaluate(image)
                scores[model_name] = score
            except Exception as e:
                logger.error(f"Error evaluating with {model_name}: {e}")
                scores[model_name] = 0.5
        
        # Calculate weighted average
        if scores:
            weights = model_weights or self.model_weights
            total_weight = 0
            weighted_sum = 0
            
            for model_name, score in scores.items():
                weight = weights.get(model_name, 1.0)
                weighted_sum += score * weight
                total_weight += weight
            
            if total_weight > 0:
                scores["combined"] = weighted_sum / total_weight
            else:
                scores["combined"] = 0.5
        else:
            scores["combined"] = 0.5
        
        return scores
    
    def get_combined_score(self, image: Union[Image.Image, torch.Tensor],
                          model_weights: Optional[Dict[str, float]] = None) -> float:
        """
        Get combined aesthetic score
        
        Args:
            image: Input image
            model_weights: Optional custom weights for models
            
        Returns:
            Combined aesthetic score (0-1)
        """
        scores = self.evaluate(image, model_weights)
        return scores.get("combined", 0.5)
    
    def set_model_weights(self, weights: Dict[str, float]) -> None:
        """
        Set weights for model combination
        
        Args:
            weights: Dictionary mapping model names to weights
        """
        for model_name, weight in weights.items():
            if model_name in self.models:
                self.model_weights[model_name] = weight
            else:
                logger.warning(f"Model {model_name} not loaded, ignoring weight")
    
    def add_model(self, model_name: str, weight: float = 1.0) -> None:
        """
        Add a new aesthetic model
        
        Args:
            model_name: Name of the model to add
            weight: Weight for the model in combination
        """
        if model_name not in self.models:
            self._load_model(model_name)
            if model_name in self.models:
                self.model_weights[model_name] = weight
    
    def remove_model(self, model_name: str) -> None:
        """
        Remove an aesthetic model
        
        Args:
            model_name: Name of the model to remove
        """
        if model_name in self.models:
            del self.models[model_name]
            del self.model_weights[model_name]
            logger.info(f"Removed model: {model_name}")
    
    def list_models(self) -> List[str]:
        """
        Get list of loaded models

        Returns:
            List of model names
        """
        return list(self.models.keys())

    def set_random_seed(self, seed: Optional[int]) -> None:
        """
        Set random seed for all models

        Args:
            seed: Random seed value (None to disable deterministic behavior)
        """
        self.random_seed = seed
        # Update seed for all loaded models
        for model in self.models.values():
            model.random_seed = seed
        logger.info(f"Random seed set to {seed} for all aesthetic models")

    def get_random_seed(self) -> Optional[int]:
        """
        Get current random seed

        Returns:
            Current random seed value
        """
        return self.random_seed
