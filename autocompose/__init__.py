"""
AutoComposeAgent - Automatic Image Composition Adjustment System

A multi-agent system for intelligent image composition optimization
following the 4KAgent architecture pattern.
"""

__version__ = "0.1.0"
__author__ = "AutoComposeAgent Team"

from .main import AutoComposeAgent
from .agents.perception import PerceptionAgent
from .agents.planning import PlanningAgent
from .agents.restoration import RestorationAgent
from .core.profile import Profile
from .core.evaluator import AestheticEvaluator
from .utils.image_ops import ImageOperations

__all__ = [
    "AutoComposeAgent",
    "PerceptionAgent",
    "PlanningAgent",
    "RestorationAgent",
    "Profile",
    "AestheticEvaluator",
    "ImageOperations"
]
