# AutoComposeAgent

AutoComposeAgent是一个智能图像构图增强系统，使用多个AI代理自动分析和改进图像美学效果。

## 🌟 核心特性

### 🧠 先进的AI规划
- **LLM思考规划**: 使用Qwen2.5、GPT-4或Claude进行步骤化推理
- **VRAM感知部署**: 根据可用GPU内存自动选择模型
- **多策略规划**: LLM思考 → MLLM指令 → 规则回退
- **链式思维**: Chain-of-Thought推理提高规划质量

### 🎨 智能图像编辑
- **MLLM增强修复**: 使用Flux.1、Bagel、InstructPix2Pix进行自然语言指令编辑
- **指令引导编辑**: 自然语言规划，生成人类可读的编辑指令
- **POEM对象编辑**: 精确的对象识别和变换预测
- **闭环反馈系统**: 迭代改进和自动质量监控
- **传统操作**: 裁剪、旋转、颜色调整、亮度控制

### 🔧 灵活部署
- **本地部署**: 完全私有的本地模型推理
- **云API支持**: 集成DashScope、OpenAI、Anthropic
- **混合模式**: 本地和云端资源间自动回退
- **容器支持**: Docker部署和资源管理
- **多模型支持**: GPT-4 Vision、Claude Vision、Llama Vision

## 🚀 快速开始

### 安装
```bash
# 克隆项目
git clone <repository-url>
cd AutoComposeAgent

# 安装依赖
pip install -r requirements.txt

# 验证安装
python run.py --help
```

### 基础使用
```bash
# 命令行使用
python run.py process input.jpg --output output.jpg

# 使用增强功能
python run.py process input.jpg --config config_enhanced.yaml --output enhanced.jpg

# 使用Qwen2.5本地推理
python run.py process input.jpg --config config_qwen.yaml --output qwen_enhanced.jpg
```

### Python编程使用
```python
from autocompose import AutoComposeAgent

# 基础使用
agent = AutoComposeAgent()
result = agent.process_image("input.jpg")
result.final_image.save("output.jpg")

# 查看结果
print(f"原始评分: {result.original_score:.3f}")
print(f"最终评分: {result.final_score:.3f}")
print(f"改进幅度: {result.improvement:.3f}")
```

## ⚙️ 配置说明

### 统一配置文件 - config.yaml
现在使用单一的完整配置文件，支持所有功能：

- **LLM思考规划**: Qwen2.5、GPT-4、Claude支持
- **MLLM图像编辑**: Flux.1、Bagel、InstructPix2Pix、GPT-4+DALL-E
- **VRAM感知部署**: 自动选择最优本地部署策略
- **多层回退机制**: LLM思考 → MLLM指令 → 规则规划
- **完整API支持**: DashScope、OpenAI、Anthropic集成

### 功能开关
通过配置文件控制功能启用：

```yaml
profile:
  enable_thinking_planning: true    # 启用LLM思考规划
  enable_mllm_restoration: true     # 启用MLLM图像编辑
  enable_feedback_loop: true        # 启用反馈循环

deployment:
  auto_select_strategy: true        # 自动选择部署策略
  prefer_local_deployment: true     # 优先本地部署
```

## 🔧 Qwen2.5部署配置

### 部署选项

#### 选项1: DashScope API（推荐）
```bash
# 安装依赖
pip install dashscope

# 设置API密钥
export DASHSCOPE_API_KEY="your_dashscope_key"

# 使用配置
python run.py process input.jpg --config config_qwen.yaml
```

#### 选项2: 本地vLLM部署
```bash
# 安装vLLM
pip install vllm

# 启动API服务器
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-72B-Instruct \
    --host 0.0.0.0 --port 8000

# 配置本地端点
# config_qwen.yaml中设置: qwen_base_url: "http://localhost:8000/v1"
```

#### 选项3: Ollama部署
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 拉取模型
ollama pull qwen2.5:72b

# 启动服务
ollama serve
```

### 自动部署策略

#### 方案A: 单H800部署（7B/14B模型）
- **VRAM需求**: 17-30GB
- **精度**: FP16或量化
- **适用**: 单卡高性能场景

#### 方案B: 多GPU/量化部署（72B模型）
- **VRAM需求**: 36-147GB
- **精度**: 4-bit量化或FP8
- **适用**: 多卡或重度量化场景

### API配置
```bash
# DashScope API
export DASHSCOPE_API_KEY="your_dashscope_key"

# OpenAI API
export OPENAI_API_KEY="your_openai_key"

# Anthropic API
export ANTHROPIC_API_KEY="your_anthropic_key"
```

## 🎯 使用场景

### 人像构图优化
```bash
python run.py process portrait.jpg --config config_enhanced.yaml --output portrait_enhanced.jpg
```
适用于：人像居中、背景杂乱、光线不佳

### 风景照片增强
```bash
python run.py process landscape.jpg --config config_qwen.yaml --output landscape_enhanced.jpg
```
适用于：地平线倾斜、构图失衡、色彩暗淡

### 批量处理
```python
from autocompose import AutoComposeAgent
from pathlib import Path

agent = AutoComposeAgent("config_enhanced.yaml")
input_dir = Path("input_images")
output_dir = Path("enhanced_images")
output_dir.mkdir(exist_ok=True)

for img_path in input_dir.glob("*.jpg"):
    result = agent.process_image(str(img_path))
    output_path = output_dir / f"enhanced_{img_path.name}"
    result.final_image.save(output_path)
    print(f"处理完成: {img_path.name} -> 改进 {result.improvement:.3f}")
```

## 🧪 示例代码

```bash
# 基础示例
python examples/simple_example.py

# 完整工作流演示
python examples/enhanced_workflow_example.py

# Qwen2.5演示
python examples/qwen_planning_example.py

# MLLM功能演示
python examples/mllm_example.py

# VRAM感知部署演示
python examples/vram_aware_deployment_example.py
```

## 🔍 故障排除

### 常见问题

#### 1. 内存不足
```
解决方案:
- 使用更小的模型配置
- 启用模型量化
- 减少批处理大小
```

#### 2. API调用失败
```
解决方案:
- 检查API密钥设置
- 验证网络连接
- 查看API使用限额
```

#### 3. 模型加载失败
```
解决方案:
- 检查磁盘空间
- 验证网络连接
- 使用镜像源下载
```

### 调试技巧
```bash
# 详细日志模式
python run.py process input.jpg --verbose --debug

# 查看系统信息
python -c "from autocompose.utils.logging_config import log_system_info; log_system_info()"

# 测试配置文件
python -c "from autocompose.core.profile import Profile; p = Profile('config.yaml'); print('配置加载成功')"
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定功能测试
pytest tests/test_enhanced_features.py

# 运行性能测试
pytest tests/performance/
```

## 🐳 Docker部署

### 构建镜像
```bash
# 构建GPU版本（推荐）
./docker/build.sh -t gpu

# 构建CPU版本
./docker/build.sh -t cpu

# 或使用docker-compose
docker-compose build
```

### 运行容器
```bash
# GPU版本
docker run --gpus all \
  -v $(pwd)/input:/app/input \
  -v $(pwd)/output:/app/output \
  -e DASHSCOPE_API_KEY="your_key" \
  autocompose-agent:gpu

# CPU版本
docker run \
  -v $(pwd)/input:/app/input \
  -v $(pwd)/output:/app/output \
  autocompose-agent:cpu

# 使用docker-compose
docker-compose up
```

### 环境变量
```bash
# API密钥
DASHSCOPE_API_KEY=your_dashscope_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# 配置文件
CONFIG_FILE=config_enhanced.yaml

# 输出设置
SAVE_INTERMEDIATE=true
VERBOSE=false
```

## 📊 性能指标

### VRAM需求
| 模型 | FP16 | 8-bit | 4-bit | 推荐策略 |
|------|------|-------|-------|----------|
| Qwen2.5-7B | 17GB | 10GB | 8GB | 方案A |
| Qwen2.5-14B | 30GB | 16GB | 10GB | 方案A |
| Qwen2.5-72B | 147GB | 72GB | 36GB | 方案B |

### 处理时间
- **感知分析**: 3-7秒
- **LLM规划**: 2-10秒（取决于模型）
- **MLLM编辑**: 5-15秒
- **总体处理**: 10-30秒

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

---

🎉 开始您的AI图像增强之旅吧！
