# AutoComposeAgent Configuration - Simplified for Testing

profile:
  # Model configuration
  perception_model: "llama-vision"
  planning_model: "qwen2.5"
  restoration_models: ["flux"]

  # Aesthetic evaluation
  aesthetic_models: ["laion-aesthetic"]
  restore_preference: "balanced"
  rollback_threshold: 0.0              # Threshold for automatic rollback
  random_seed: 42                      # Random seed for reproducible evaluation

  # Available operations
  tasks: ["crop", "rotate", "recenter", "color_adjust", "brightness"]

  # Basic settings
  max_iterations: 3
  enable_feedback_loop: false
  enable_thinking_planning: true       # Simplified for testing
  enable_mllm_restoration: true         # Enable MLLM editing for testing
  enable_planning_chain: true           # Enable Planning Chain architecture

  # Planning Chain settings
  max_replanning_attempts: 3            # Maximum replanning attempts
  max_local_rollbacks: 2                # Maximum local rollbacks per plan
  max_global_rollbacks: 1               # Maximum global rollbacks per execution
  min_improvement_threshold: -0.1       # Minimum improvement threshold for severe degradation

  # Feedback settings
  feedback_success_threshold: 0.005      # Threshold for success feedback
  feedback_warning_threshold: -0.02     # Threshold for warning feedback
  feedback_failure_threshold: -0.1      # Threshold for failure feedback

  # Processing settings
  device: "cuda"
  batch_size: 1

# MLLM Restoration Configuration
mllm_restoration:
  model_priority: ['flux']
  enable_automatic_rollback: true
  flux:
    model_id: 'black-forest-labs/FLUX.1-Kontext-dev'
    guidance_scale: 1.5
    num_inference_steps: 20
    # strength: 0.8
    max_image_size: 1024

# API Configuration (simplified)
api:
  openai_api_key: null
  anthropic_api_key: null

# Logging Configuration
logging:
  level: "INFO"
  save_intermediate: true
  save_rollback_points: true
  output_format: "jpg"

# Performance Configuration
performance:
  use_gpu: true
  num_workers: 1
  memory_efficient: true
