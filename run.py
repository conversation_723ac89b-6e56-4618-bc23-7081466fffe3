#!/usr/bin/env python3
"""
AutoComposeAgent CLI Interface

Command-line interface for the AutoComposeAgent image composition adjustment system.
Based on 4KAgent architecture with Execute-Reflect-Rollback workflow.
"""

# 设置 CUDA 确定性行为的环境变量（必须在导入 torch 之前设置）
import os
if 'CUBLAS_WORKSPACE_CONFIG' not in os.environ:
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'

"""
核心逻辑与代码结构说明
===================

整体架构
-------
这是AutoComposeAgent的命令行界面，实现了基于4KAgent架构的图像构图增强系统。
整个系统遵循"感知(Perception) → 规划(Planning) → 恢复(Restoration)"的工作流，
并使用"执行-反思-回滚"(Execute-Reflect-Rollback)机制来保证处理质量。

主要功能
-------
1. 单图像处理：分析图像构图问题并进行自动优化
2. 批量处理：处理整个目录的图像
3. 图像比较：比较两个图像的构图质量
4. 系统配置：查看和初始化系统配置

关键函数及作用
-----------
- create_before_after_visualization: 创建处理前后对比可视化
- handle_exception: 集中处理异常，简化错误处理逻辑
- initialize_agent: 初始化AutoComposeAgent实例
- display_analysis_results: 显示图像分析结果
- display_plan: 显示调整计划
- display_full_results: 显示完整的处理结果
- main: 主命令，处理单个图像的完整工作流
- cli: 附加功能命令组

执行流程
-------
1. 命令行解析：通过Click库解析命令行参数
2. 代理初始化：使用initialize_agent函数初始化AutoComposeAgent
3. 图像处理：
   a. 分析模式(analyze-only)：仅分析图像问题不进行修复
   b. 完整处理模式：执行完整的感知-规划-恢复工作流
4. 结果呈现：显示分析结果或处理结果
5. 可选操作：生成可视化对比、保存中间步骤

使用方式
-------
主命令：
  python run.py --input image.png --output enhanced.png [options]

辅助命令：
  python run.py batch input_dir output_dir [options]
  python run.py compare image1.png image2.png
  python run.py info
  python run.py init-config [--output config.yaml]

代码扩展点
---------
1. 添加新的命令行参数：在main函数的@click.option装饰器中添加
2. 扩展可视化方式：修改create_before_after_visualization函数
3. 增加新的辅助命令：在cli组中添加新的@cli.command
"""

import click
import logging
from pathlib import Path
import sys
import time
import matplotlib.pyplot as plt
from PIL import Image

from autocompose.main import AutoComposeAgent
from autocompose.core.profile import Profile

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_before_after_visualization(original_path, enhanced_path, output_path, result_data):
    """Create before/after comparison visualization"""
    try:
        # Load images
        original = Image.open(original_path).convert('RGB')
        enhanced = Image.open(enhanced_path).convert('RGB')

        # Create figure with subplots
        fig, axes = plt.subplots(1, 2, figsize=(15, 8))
        fig.suptitle('AutoComposeAgent - Image Composition Enhancement', fontsize=16, fontweight='bold')

        # Original image
        axes[0].imshow(original)
        axes[0].set_title(f'Original\nScore: {result_data["perception"]["initial_score"]:.3f}',
                         fontsize=12, fontweight='bold')
        axes[0].axis('off')

        # Enhanced image
        axes[1].imshow(enhanced)
        axes[1].set_title(f'Enhanced\nScore: {result_data["restoration"]["final_score"]:.3f} '
                         f'(+{result_data["restoration"]["actual_improvement"]:.3f})',
                         fontsize=12, fontweight='bold', color='green')
        axes[1].axis('off')

        # Add processing info
        info_text = f"""Processing Summary:
• Issues Detected: {len(result_data["perception"]["composition_issues"])}
• Tasks Executed: {result_data["planning"]["num_instructions"]}
• Success Rate: {result_data["restoration"]["success_rate"]:.1%}
• Processing Time: {result_data["processing_time"]:.2f}s"""

        fig.text(0.02, 0.02, info_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()

        logger.info(f"Visualization saved to: {output_path}")

    except Exception as e:
        logger.error(f"Failed to create visualization: {e}")


def handle_exception(e, verbose=False):
    """集中处理异常"""
    click.echo(f"❌ Error: {e}", err=True)
    if verbose:
        import traceback
        traceback.print_exc()
    sys.exit(1)


def initialize_agent(config_path):
    """初始化代理"""
    try:
        return AutoComposeAgent(config_path)
    except Exception as e:
        handle_exception(e)


def display_analysis_results(result):
    """显示分析结果"""
    click.echo(f"📸 Content: {result['content_description']}")
    click.echo(f"📊 Overall Score: {result['overall_score']:.3f}")

    if result['composition_issues']:
        click.echo(f"\n🚨 Composition Issues Detected ({len(result['composition_issues'])}):")
        for i, issue in enumerate(result['composition_issues'], 1):
            click.echo(f"   {i}. {issue}")

    if result['recommendations']:
        click.echo(f"\n💡 Recommendations ({len(result['recommendations'])}):")
        for i, rec in enumerate(result['recommendations'], 1):
            click.echo(f"   {i}. {rec}")


def display_plan(plan, show_plan, verbose):
    """显示调整计划"""
    if show_plan or verbose:
        click.echo(f"\n📋 Suggested Adjustment Plan:")
        click.echo(f"   Instructions ({plan['num_instructions']}): {', '.join(plan['instructions'])}")


def display_full_results(result, output_path, show_plan, verbose):
    """显示完整的处理结果"""
    # Perception results
    perception = result['perception']
    click.echo(f"🔍 Perception Phase:")
    click.echo(f"   Content: {perception['content_description']}")
    click.echo(f"   Initial Score: {perception['initial_score']:.3f}")
    click.echo(f"   Issues Found: {len(perception['composition_issues'])}")

    # Planning results
    planning = result['planning']
    click.echo(f"\n📋 Planning Phase:")
    click.echo(f"   Tasks Generated: {planning['num_instructions']}")
    click.echo(f"   Target Score: {planning.get('target_score', 0.0):.3f}")
    click.echo(f"   Expected Improvement: {planning.get('estimated_improvement', 0.0):.3f}")

    if show_plan or verbose:
        click.echo(f"   Reasoning: {planning['reasoning']}")

    # Restoration results
    restoration = result['restoration']
    click.echo(f"\n🔧 Restoration Phase (Execute-Reflect-Rollback):")
    click.echo(f"   Final Score: {restoration['final_score']:.3f}")
    click.echo(f"   Actual Improvement: {restoration['actual_improvement']:.3f}")
    click.echo(f"   Success Rate: {restoration['success_rate']:.1%}")

    # Output info
    click.echo(f"\n📁 Output:")
    click.echo(f"   Enhanced Image: {output_path}")

    # Show improvement summary
    improvement = restoration['actual_improvement']
    if improvement > 0.1:
        click.echo(f"\n🎉 Significant improvement achieved! (+{improvement:.3f})")
    elif improvement > 0:
        click.echo(f"\n✨ Moderate improvement achieved! (+{improvement:.3f})")
    else:
        click.echo(f"\n⚠️  Limited improvement. Original may already be well-composed.")


@click.command()
@click.option('--config', '-c', type=click.Path(exists=True),
              help='Configuration file path (default: config.yaml)')
@click.option('--input', '-i', required=True, type=click.Path(exists=True),
              help='Input image path')
@click.option('--output', '-o', required=True, type=click.Path(),
              help='Output image path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--analyze-only', is_flag=True,
              help='Only analyze image without making adjustments')
@click.option('--save-intermediate', is_flag=True,
              help='Save intermediate processing steps')
@click.option('--visualize', is_flag=True,
              help='Create before/after comparison visualization')
@click.option('--show-plan', is_flag=True,
              help='Display detailed adjustment plan')
def main(config, input, output, verbose, analyze_only, save_intermediate, visualize, show_plan):
    """
    AutoComposeAgent - 4KAgent Architecture Image Composition Enhancement

    Execute the complete Perception → Planning → Restoration workflow
    with Execute-Reflect-Rollback mechanism.

    Example:
        python run.py --config config.yaml --input image.png --output enhanced.png
    """
    # Setup logging
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Use default config if not specified
    if not config:
        config = "config.yaml"
        if not Path(config).exists():
            logger.warning(f"Config file {config} not found, using defaults")
            config = None

    try:
        # Initialize AutoComposeAgent
        click.echo("🚀 Initializing AutoComposeAgent System...")
        agent = initialize_agent(config)

        # Display system info
        if verbose:
            info = agent.get_system_info()
            click.echo(f"📋 System Configuration:")
            click.echo(f"   Perception Model: {info['profile']['perception_model']}")
            click.echo(f"   Aesthetic Models: {', '.join(info['profile']['aesthetic_models'] or [])}")
            click.echo(f"   Enabled Tasks: {', '.join(info['profile']['enabled_tasks'] or [])}")
            click.echo(f"   Device: {info['profile']['device']}")

        input_path = Path(input)
        output_path = Path(output)

        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)

        if analyze_only:
            # Analysis-only mode
            click.echo(f"\n🔍 Analyzing Image: {input_path}")
            click.echo("=" * 60)

            result = agent.analyze_only(input_path)
            
            display_analysis_results(result)
            display_plan(result['suggested_plan'], show_plan, verbose)
        else:
            # Full processing mode
            click.echo(f"\n🎨 Processing Image: {input_path}")
            click.echo("=" * 60)

            # Execute complete workflow
            start_time = time.time()
            result = agent.process_image(input_path, output_path, save_intermediate)
            result['processing_time'] = time.time() - start_time
            
            click.echo(f"\n✅ Processing Complete! ({result['processing_time']:.2f}s)")
            click.echo("=" * 50)

            display_full_results(result, output_path, show_plan, verbose)

            if save_intermediate:
                click.echo(f"   Intermediate Steps: {output_path.parent}/{output_path.stem}_*")

            # Create visualization if requested
            if visualize:
                viz_path = output_path.parent / f"{output_path.stem}_comparison.png"
                create_before_after_visualization(str(input_path), str(output_path),
                                               str(viz_path), result)

    except Exception as e:
        handle_exception(e, verbose)


# Additional utility commands

@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """AutoComposeAgent - Additional Utility Commands"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize context
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose


@cli.command()
@click.argument('input_image', type=click.Path(exists=True))
@click.argument('output_image', type=click.Path())
@click.option('--save-intermediate', is_flag=True, help='Save intermediate processing steps')
@click.option('--analyze-only', is_flag=True, help='Only analyze without making adjustments')
@click.pass_context
def process(ctx, input_image, output_image, save_intermediate, analyze_only):
    """Process a single image to improve composition (Legacy command)"""
    click.echo("⚠️  Note: This is a legacy command. Use the main interface instead:")
    click.echo(f"   python run.py --input {input_image} --output {output_image}")

    try:
        # Initialize agent
        agent = initialize_agent(ctx.obj.get('config_path'))

        if analyze_only:
            result = agent.analyze_only(input_image)
            click.echo(f"\n📸 Analysis Results: Score {result['overall_score']:.3f}")
        else:
            result = agent.process_image(input_image, output_image, save_intermediate)
            click.echo(f"\n✅ Processing Complete! Improvement: {result['restoration']['actual_improvement']:.3f}")
    except Exception as e:
        handle_exception(e, ctx.obj.get('verbose', False))


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.argument('output_dir', type=click.Path())
@click.option('--pattern', default='*.png', help='File pattern to match (default: *.png)')
@click.option('--save-intermediate', is_flag=True, help='Save intermediate processing steps')
@click.pass_context
def batch(ctx, input_dir, output_dir, pattern, save_intermediate):
    """Process multiple images in batch"""
    try:
        agent = initialize_agent(ctx.obj.get('config_path'))
        click.echo(f"🔄 Batch processing: {input_dir} → {output_dir}")

        results = agent.batch_process(input_dir, output_dir, pattern)
        successful = sum(1 for r in results if r.get('success', True))

        click.echo(f"\n✅ Batch Complete: {successful}/{len(results)} successful")

        if successful > 0:
            improvements = [r['restoration']['actual_improvement']
                            for r in results if 'restoration' in r]
            if improvements:
                avg_improvement = sum(improvements) / len(improvements)
                click.echo(f"Average Improvement: {avg_improvement:.3f}")
    except Exception as e:
        handle_exception(e, ctx.obj.get('verbose', False))


@cli.command()
@click.argument('image1', type=click.Path(exists=True))
@click.argument('image2', type=click.Path(exists=True))
@click.pass_context
def compare(ctx, image1, image2):
    """Compare two images"""
    try:
        agent = initialize_agent(ctx.obj.get('config_path'))
        result = agent.compare_images(image1, image2)

        click.echo(f"📊 Comparison: {result['better_image']} is better")
        click.echo(f"   Scores: {result['image1_score']:.3f} vs {result['image2_score']:.3f}")
        click.echo(f"   Difference: {result['score_difference']:.3f}")
    except Exception as e:
        handle_exception(e, ctx.obj.get('verbose', False))


@cli.command()
@click.pass_context
def info(ctx):
    """Show system information and configuration"""
    try:
        agent = initialize_agent(ctx.obj.get('config_path'))
        info_data = agent.get_system_info()

        click.echo("🔧 AutoComposeAgent System Information")
        click.echo("=" * 50)

        profile = info_data['profile']
        click.echo(f"Perception Model: {profile.get('perception_model', 'Not configured')}")
        click.echo(f"Aesthetic Models: {', '.join(profile.get('aesthetic_models', []))}")
        click.echo(f"Enabled Tasks: {', '.join(profile.get('enabled_tasks', []))}")
        click.echo(f"Device: {profile.get('device', 'Not configured')}")
    except Exception as e:
        handle_exception(e, ctx.obj.get('verbose', False))


@cli.command()
@click.option('--output', '-o', type=click.Path(), help='Output configuration file path')
def init_config(output):
    """Initialize a new configuration file"""
    try:
        if not output:
            output = "config.yaml"

        profile = Profile("config.yaml")
        profile.save_config(output)

        click.echo(f"✅ Configuration file created: {output}")
        click.echo("Edit this file to customize your settings.")
    except Exception as e:
        handle_exception(e)


# Make the main command the default when run directly
if __name__ == '__main__':
    import sys

    # If no arguments provided, show help
    if len(sys.argv) == 1:
        main(['--help'])
    # If arguments don't start with a subcommand, use main command
    elif not any(sys.argv[1].startswith(cmd) for cmd in ['batch', 'compare', 'info', 'init-config', 'process']):
        main()
    else:
        cli()
